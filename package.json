{"name": "front-development-tools-mcp", "version": "2.1.0", "description": "mcp service for front development tools", "type": "module", "main": "dist/index.js", "bin": {"front-development-tools-mcp": "dist/cli.js"}, "files": ["dist", "README.md"], "scripts": {"build": "tsup --dts", "type-check": "tsc --noEmit", "test": "jest", "start": "node dist/cli.js", "start:cli": "cross-env NODE_ENV=cli node dist/cli.js", "start:http": "node dist/cli.js", "dev": "cross-env NODE_ENV=development tsup --watch", "dev:test": "cross-env NODE_ENV=development tsup --watch", "dev:cli": "cross-env NODE_ENV=development tsup --watch -- --stdio", "lint": "eslint . --ext .ts", "prepublishOnly": "yarn build"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git"}, "keywords": ["mcp", "typescript"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.10.2", "@types/yargs": "^17.0.33", "child_process": "^1.0.2", "cross-env": "^7.0.3", "dotenv": "^16.6.1", "express": "^4.21.2", "node-fetch": "^2.0.0", "yargs": "^17.7.2", "zod": "^3.24.2"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@types/express": "^5.0.0", "@types/node": "^20.17.0", "@typescript-eslint/eslint-plugin": "^8.24.0", "@typescript-eslint/parser": "^8.24.0", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "prettier": "^3.5.0", "tsup": "^8.4.0", "tsx": "^4.19.2", "typescript": "^5.7.3"}}