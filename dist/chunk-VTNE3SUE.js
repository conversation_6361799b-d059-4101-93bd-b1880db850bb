// src/cli.ts
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { config } from "dotenv";
import { resolve as resolve2 } from "path";

// src/config.ts
import { config as loadEnv } from "dotenv";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { resolve } from "path";
function getServerConfig(isStdioMode) {
  const argv = yargs(hideBin(process.argv)).options({
    env: {
      type: "string",
      description: "Path to custom .env file to load environment variables from"
    },
    port: {
      type: "number",
      description: "Port to run the server on"
    }
  }).help().parseSync();
  let envFilePath;
  if (argv["env"]) {
    envFilePath = resolve(argv["env"]);
  } else {
    envFilePath = resolve(process.cwd(), ".env");
  }
  loadEnv({ path: envFilePath, override: true });
  const config2 = {
    port: 3002
  };
  if (argv.port) {
    config2.port = argv.port;
  } else if (process.env.PORT) {
    config2.port = parseInt(process.env.PORT, 10);
  }
  return {
    ...config2
  };
}

// src/server.ts
import { randomUUID } from "crypto";
import express from "express";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js";
import "@modelcontextprotocol/sdk/server/mcp.js";

// src/utils/logger.ts
var Logger = {
  isHTTP: false,
  log: (...args) => {
    if (Logger.isHTTP) {
      console.log("[INFO]", ...args);
    } else {
      console.error("[INFO]", ...args);
    }
  },
  error: (...args) => {
    console.error("[ERROR]", ...args);
  }
};

// src/server.ts
var httpServer = null;
var transports = {
  streamable: {},
  sse: {}
};
async function startHttpServer(port, mcpServer) {
  const app = express();
  app.use("/mcp", express.json());
  app.post("/mcp", async (req, res) => {
    Logger.log("Received StreamableHTTP request");
    const sessionId = req.headers["mcp-session-id"];
    let transport;
    if (sessionId && transports.streamable[sessionId]) {
      Logger.log("Reusing existing StreamableHTTP transport for sessionId", sessionId);
      transport = transports.streamable[sessionId];
    } else if (!sessionId && isInitializeRequest(req.body)) {
      Logger.log("New initialization request for StreamableHTTP sessionId", sessionId);
      transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => randomUUID(),
        onsessioninitialized: (sessionId2) => {
          transports.streamable[sessionId2] = transport;
        }
      });
      transport.onclose = () => {
        if (transport.sessionId) {
          delete transports.streamable[transport.sessionId];
        }
      };
      await mcpServer.connect(transport);
    } else {
      Logger.log("Invalid request:", req.body);
      res.status(400).json({
        jsonrpc: "2.0",
        error: {
          code: -32e3,
          message: "Bad Request: No valid session ID provided"
        },
        id: null
      });
      return;
    }
    let progressInterval = null;
    const progressToken = req.body.params?._meta?.progressToken;
    let progress = 0;
    if (progressToken) {
      Logger.log(
        `Setting up progress notifications for token ${progressToken} on session ${sessionId}`
      );
      progressInterval = setInterval(async () => {
        Logger.log("Sending progress notification", progress);
        await mcpServer.server.notification({
          method: "notifications/progress",
          params: {
            progress,
            progressToken
          }
        });
        progress++;
      }, 1e3);
    }
    Logger.log("Handling StreamableHTTP request");
    await transport.handleRequest(req, res, req.body);
    if (progressInterval) {
      clearInterval(progressInterval);
    }
    Logger.log("StreamableHTTP request handled");
  });
  const handleSessionRequest = async (req, res) => {
    const sessionId = req.headers["mcp-session-id"];
    if (!sessionId || !transports.streamable[sessionId]) {
      res.status(400).send("Invalid or missing session ID");
      return;
    }
    Logger.log(`Received session termination request for session ${sessionId}`);
    try {
      const transport = transports.streamable[sessionId];
      await transport.handleRequest(req, res);
    } catch (error) {
      console.error("Error handling session termination:", error);
      if (!res.headersSent) {
        res.status(500).send("Error processing session termination");
      }
    }
  };
  app.get("/mcp", handleSessionRequest);
  app.delete("/mcp", handleSessionRequest);
  app.get("/sse", async (req, res) => {
    Logger.log("Establishing new SSE connection");
    const transport = new SSEServerTransport("/messages", res);
    Logger.log(`New SSE connection established for sessionId ${transport.sessionId}`);
    Logger.log("/sse request headers:", req.headers);
    Logger.log("/sse request body:", req.body);
    transports.sse[transport.sessionId] = transport;
    res.on("close", () => {
      delete transports.sse[transport.sessionId];
    });
    await mcpServer.connect(transport);
  });
  app.post("/messages", async (req, res) => {
    const sessionId = req.query.sessionId;
    const transport = transports.sse[sessionId];
    if (transport) {
      Logger.log(`Received SSE message for sessionId ${sessionId}`);
      Logger.log("/messages request headers:", req.headers);
      Logger.log("/messages request body:", req.body);
      await transport.handlePostMessage(req, res);
    } else {
      res.status(400).send(`No transport found for sessionId ${sessionId}`);
      return;
    }
  });
  httpServer = app.listen(port, () => {
    Logger.log(`HTTP server listening on port ${port}`);
    Logger.log(`SSE endpoint available at http://localhost:${port}/sse`);
    Logger.log(`Message endpoint available at http://localhost:${port}/messages`);
    Logger.log(`StreamableHTTP endpoint available at http://localhost:${port}/mcp`);
  });
  process.on("SIGINT", async () => {
    Logger.log("Shutting down server...");
    await closeTransports(transports.sse);
    await closeTransports(transports.streamable);
    Logger.log("Server shutdown complete");
    process.exit(0);
  });
}
async function closeTransports(transports2) {
  for (const sessionId in transports2) {
    try {
      await transports2[sessionId]?.close();
      delete transports2[sessionId];
    } catch (error) {
      console.error(`Error closing transport for session ${sessionId}:`, error);
    }
  }
}

// src/cli.ts
import { McpServer as McpServer2 } from "@modelcontextprotocol/sdk/server/mcp.js";

// src/mcp-tools/npm-publish.ts
import { z } from "zod";

// src/utils/memory-store.ts
var MemoryStoreManager = class {
  store = {};
  /**
   * 设置数据
   * @param key 键名
   * @param value 值
   */
  set(key, value) {
    this.store[key] = value;
  }
  /**
   * 获取数据
   * @param key 键名
   * @returns 值或undefined
   */
  get(key) {
    return this.store[key];
  }
  /**
   * 删除数据
   * @param key 键名
   * @returns 是否删除成功
   */
  delete(key) {
    if (key in this.store) {
      delete this.store[key];
      return true;
    }
    return false;
  }
  /**
   * 检查键是否存在
   * @param key 键名
   * @returns 是否存在
   */
  has(key) {
    return key in this.store;
  }
  /**
   * 清空所有数据
   */
  clear() {
    this.store = {};
  }
  /**
   * 获取所有键
   * @returns 键名数组
   */
  keys() {
    return Object.keys(this.store);
  }
  /**
   * 获取存储的数据数量
   * @returns 数据数量
   */
  size() {
    return Object.keys(this.store).length;
  }
};
var memoryStore = new MemoryStoreManager();
var memory_store_default = memoryStore;

// src/packages/npm-publish/gitbash.ts
import { execSync } from "child_process";
import fs from "fs";
import path from "path";

// src/utils/patch.ts
import { fileURLToPath } from "url";
import { dirname } from "path";
var __filename = fileURLToPath(import.meta.url);
var __dirname = dirname(__filename);

// src/gitlib/api.ts
import fetch from "node-fetch";
function request(path2, options = {}) {
  const rootUrl = "https://gitlab.qima-inc.com/api/v4";
  const url = `${rootUrl}${path2}`;
  const maxRetries = 3;
  const makeRequest = async (attempt = 1) => {
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "PRIVATE-TOKEN": process.env.private_token
        },
        ...options
      });
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`[GitLab API] Attempt ${attempt} failed for ${path2}:`, error.message);
      if (attempt >= maxRetries) {
        console.error(`[GitLab API] All ${maxRetries} attempts failed for ${path2}`);
        throw error;
      }
      const delay = attempt * 300;
      console.log(`[GitLab API] Retrying in ${delay}ms... (attempt ${attempt + 1}/${maxRetries})`);
      await new Promise((resolve3) => setTimeout(resolve3, delay));
      return makeRequest(attempt + 1);
    }
  };
  return makeRequest();
}
var createBranch = ({
  appName,
  branchName,
  targetBranchName = "master"
}) => {
  const projectId = getProjectId(appName);
  return request(
    `/projects/${projectId}/repository/branches?branch=${branchName}&ref=${targetBranchName}`
  );
};
var getBranch = ({ appName, branchName }) => {
  const encodeBranchName = encodeURIComponent(branchName);
  const projectId = getProjectId(appName);
  return request(`/projects/${projectId}/repository/branches/${encodeBranchName}`, {
    method: "GET"
  });
};
var getBranchs = ({ appName, search }) => {
  const projectId = getProjectId(appName || "wsc");
  return request(
    `/projects/${projectId}/repository/branches?search=` + encodeURIComponent(search),
    {
      method: "GET"
    }
  );
};
var getProjectInfo = async ({ appName }) => {
  const projectId = getProjectId(appName);
  return request(`/projects/${projectId}`, {
    method: "GET"
  });
};
var getRepositoryFileContent = async ({
  appName,
  filePath,
  branch
}) => {
  const projectId = getProjectId(appName);
  return request(
    `/projects/${projectId}/repository/files/${encodeURIComponent(
      filePath
    )}?ref=${branch}`,
    {
      method: "GET"
    }
  );
};
var updateRepositoryFileContent = async ({
  appName,
  filePath,
  branch,
  content,
  commit_message
}) => {
  const projectId = getProjectId(appName);
  return request(
    `/projects/${projectId}/repository/files/${encodeURIComponent(filePath)}`,
    {
      method: "PUT",
      body: JSON.stringify({
        branch,
        content,
        commit_message
      })
    }
  );
};

// src/gitlib/git-projects.json
var git_projects_default = [
  {
    id: 1414,
    description: "\u5FAE\u4FE1\u5C0F\u7A0B\u5E8F\u4E4B\u5FAE\u5546\u57CE\uFF0C\u8FD9\u4E2A\u4EE3\u7801\u4E0D\u8981\u6CC4\u9732\u5230\u5916\u90E8\uFF0C\u53EF\u80FD\u6709\u5B89\u5168\u98CE\u9669",
    name: "wsc",
    name_with_namespace: "weapp / wsc",
    path: "wsc",
    path_with_namespace: "weapp/wsc",
    created_at: "2016-11-16T01:50:03.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/1414/WechatIMG3.jpeg",
    forks_count: 2,
    star_count: 45,
    last_activity_at: "2025-07-12T12:28:42.617Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11795,
    description: "",
    name: "wsc-tee-h5",
    name_with_namespace: "weapp / wsc-tee-h5",
    path: "wsc-tee-h5",
    path_with_namespace: "weapp/wsc-tee-h5",
    created_at: "2021-03-30T12:15:17.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-h5.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-h5.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-h5",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-h5/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 9,
    last_activity_at: "2025-07-12T12:28:38.823Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 12047,
    description: "",
    name: "ext-tee-wsc-trade",
    name_with_namespace: "weapp / ext-tee-wsc-trade",
    path: "ext-tee-wsc-trade",
    path_with_namespace: "weapp/ext-tee-wsc-trade",
    created_at: "2021-05-10T03:22:47.147Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-wsc-trade.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-12T12:28:38.016Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11654,
    description: "\u8D44\u4EA7 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-assets",
    name_with_namespace: "weapp / ext-tee-assets",
    path: "ext-tee-assets",
    path_with_namespace: "weapp/ext-tee-assets",
    created_at: "2021-03-12T02:57:31.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-assets.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-assets.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-assets",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-assets/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-12T12:28:37.314Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11809,
    description: "\u5546\u54C1 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93\r\n",
    name: "ext-tee-wsc-goods",
    name_with_namespace: "weapp / ext-tee-wsc-goods",
    path: "ext-tee-wsc-goods",
    path_with_namespace: "weapp/ext-tee-wsc-goods",
    created_at: "2021-04-01T06:24:03.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-wsc-goods.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-goods/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-12T12:28:36.608Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 15409,
    description: "",
    name: "npmpublish",
    name_with_namespace: "xujiazheng / npmpublish",
    path: "npmpublish",
    path_with_namespace: "xujiazheng/npmpublish",
    created_at: "2025-06-17T07:09:53.775Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:xujiazheng/npmpublish.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/xujiazheng/npmpublish.git",
    web_url: "https://gitlab.qima-inc.com/xujiazheng/npmpublish",
    readme_url: "https://gitlab.qima-inc.com/xujiazheng/npmpublish/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-12T06:09:29.454Z",
    namespace: {
      id: 3020,
      name: "xujiazheng",
      path: "xujiazheng",
      kind: "user",
      full_path: "xujiazheng",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/xujiazheng"
    }
  },
  {
    id: 10345,
    description: "\u96F6\u552E PC \u540E\u53F0\u5C65\u7EA6\u6A21\u5757",
    name: "retail-node-fulfillment",
    name_with_namespace: "retail-web / retail-node-fulfillment",
    path: "retail-node-fulfillment",
    path_with_namespace: "retail-web/retail-node-fulfillment",
    created_at: "2020-08-20T03:40:06.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-fulfillment.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-fulfillment/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-12T05:47:42.893Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 5808,
    description: "",
    name: "retail-node-order",
    name_with_namespace: "retail-web / retail-node-order",
    path: "retail-node-order",
    path_with_namespace: "retail-web/retail-node-order",
    created_at: "2018-10-10T02:20:24.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-order.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-order.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-order",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-order/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-12T05:47:28.590Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 4950,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u8BA2\u5355",
    name: "wsc-pc-trade",
    name_with_namespace: "wsc-node / wsc-pc-trade",
    path: "wsc-pc-trade",
    path_with_namespace: "wsc-node/wsc-pc-trade",
    created_at: "2018-06-11T03:19:44.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-trade.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-trade/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 1,
    star_count: 6,
    last_activity_at: "2025-07-12T05:46:17.136Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 11108,
    description: "\u50A8\u503Cpc",
    name: "retail-pc-prepaid",
    name_with_namespace: "retail-web / retail-pc-prepaid",
    path: "retail-pc-prepaid",
    path_with_namespace: "retail-web/retail-pc-prepaid",
    created_at: "2020-12-16T03:42:43.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-pc-prepaid.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-pc-prepaid/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-12T03:15:22.684Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 11109,
    description: "\u50A8\u503CH5",
    name: "retail-h5-prepaid",
    name_with_namespace: "retail-web / retail-h5-prepaid",
    path: "retail-h5-prepaid",
    path_with_namespace: "retail-web/retail-h5-prepaid",
    created_at: "2020-12-16T03:45:15.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-h5-prepaid.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-h5-prepaid/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-12T00:05:12.340Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 10002,
    description: "\u591A\u7AEF\u6846\u67B6",
    name: "tee",
    name_with_namespace: "fe-middle-platform / tee",
    path: "tee",
    path_with_namespace: "fe-middle-platform/tee",
    created_at: "2020-07-08T02:32:42.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/tee.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/tee.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/tee",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/tee/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-12T00:05:11.306Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 10075,
    description: "\u591A\u7AEF\u5C0F\u7A0B\u5E8F base \u4ED3\u5E93",
    name: "wsc-tee-base",
    name_with_namespace: "weapp / wsc-tee-base",
    path: "wsc-tee-base",
    path_with_namespace: "weapp/wsc-tee-base",
    created_at: "2020-07-16T02:33:09.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-base.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-base.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-base",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-base/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-12T00:05:10.718Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 6197,
    description: "\u96F6\u552E\u524D\u7AEF\uFF08\u5546\u54C1\uFF09 Node.js \u9879\u76EE",
    name: "retail-node-goods",
    name_with_namespace: "retail-web / retail-node-goods",
    path: "retail-node-goods",
    path_with_namespace: "retail-web/retail-node-goods",
    created_at: "2018-12-05T02:20:41.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-goods.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-goods.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-goods",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-goods/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6197/ls-logo_4.jpg",
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-12T00:05:07.920Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 6081,
    description: "\u96F6\u552E\u524D\u7AEF\u5E97\u94FA Node \u9879\u76EE",
    name: "retail-node-shop",
    name_with_namespace: "retail-web / retail-node-shop",
    path: "retail-node-shop",
    path_with_namespace: "retail-web/retail-node-shop",
    created_at: "2018-11-21T12:00:11.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-shop.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-shop.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-shop",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-shop/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-12T00:05:06.181Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 6184,
    description: "\u4E00\u4E9B\u5C0F\u7684\u4E1A\u52A1\u6A21\u5757\uFF0C\u76EE\u524D\u5305\u542B dashboard tool minapp",
    name: "retail-node-v2",
    name_with_namespace: "retail-web / retail-node-v2",
    path: "retail-node-v2",
    path_with_namespace: "retail-web/retail-node-v2",
    created_at: "2018-12-04T02:59:02.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-v2.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-v2.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-v2",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-v2/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-12T00:05:05.715Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 5136,
    description: "Iron H5 \u4E1A\u52A1\u62C6\u5206\uFF1A\u8D44\u4EA7",
    name: "wsc-h5-assets",
    name_with_namespace: "wsc-node / wsc-h5-assets",
    path: "wsc-h5-assets",
    path_with_namespace: "wsc-node/wsc-h5-assets",
    created_at: "2018-07-11T03:43:04.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-assets.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-assets/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T22:00:01.712Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6575,
    description: "\u7EDF\u4E00\u6536\u94F6\u53F0\u9879\u76EE",
    name: "assets-cashier-source",
    name_with_namespace: "fe-assets / assets-cashier-source",
    path: "assets-cashier-source",
    path_with_namespace: "fe-assets/assets-cashier-source",
    created_at: "2019-01-16T09:33:54.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-assets/assets-cashier-source.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source.git",
    web_url: "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source",
    readme_url: "https://gitlab.qima-inc.com/fe-assets/assets-cashier-source/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T22:00:01.394Z",
    namespace: {
      id: 1442,
      name: "fe-assets",
      path: "fe-assets",
      kind: "group",
      full_path: "fe-assets",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/fe-assets"
    }
  },
  {
    id: 4953,
    description: "",
    name: "cert",
    name_with_namespace: "fe / cert",
    path: "cert",
    path_with_namespace: "fe/cert",
    created_at: "2018-06-11T06:19:23.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/cert.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/cert.git",
    web_url: "https://gitlab.qima-inc.com/fe/cert",
    readme_url: "https://gitlab.qima-inc.com/fe/cert/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T22:00:01.219Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5243,
    description: "\u5BF9\u8D26\u5E73\u53F0\uFF0C\u524D\u7AEF\u5E94\u7528",
    name: "pay-check-web",
    name_with_namespace: "fe / pay-check-web",
    path: "pay-check-web",
    path_with_namespace: "fe/pay-check-web",
    created_at: "2018-07-25T02:33:38.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/pay-check-web.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/pay-check-web.git",
    web_url: "https://gitlab.qima-inc.com/fe/pay-check-web",
    readme_url: "https://gitlab.qima-inc.com/fe/pay-check-web/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T22:00:00.901Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4949,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u5546\u54C1",
    name: "wsc-pc-goods",
    name_with_namespace: "wsc-node / wsc-pc-goods",
    path: "wsc-pc-goods",
    path_with_namespace: "wsc-node/wsc-pc-goods",
    created_at: "2018-06-11T03:15:40.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-goods.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-goods/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 7,
    last_activity_at: "2025-07-11T16:49:43.992Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 15347,
    description: "\u6709\u8D5E\u78B0\u78B0\u8D34\u5C0F\u7A0B\u5E8F\uFF08\u652F\u4ED8\u5B9D\uFF0C\u5FAE\u4FE1\uFF09\u9879\u76EE",
    name: "zan-ppt",
    name_with_namespace: "weapp / zan-ppt",
    path: "zan-ppt",
    path_with_namespace: "weapp/zan-ppt",
    created_at: "2025-05-30T08:54:27.060Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/zan-ppt.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/zan-ppt.git",
    web_url: "https://gitlab.qima-inc.com/weapp/zan-ppt",
    readme_url: "https://gitlab.qima-inc.com/weapp/zan-ppt/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T10:42:32.968Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11993,
    description: "\u63D0\u4F9B SCRM \u516C\u7528\u80FD\u529B\u7684 NPM \u5305",
    name: "Scrm Packages Mono",
    name_with_namespace: "fe / Scrm Packages Mono",
    path: "scrm-packages-mono",
    path_with_namespace: "fe/scrm-packages-mono",
    created_at: "2021-04-27T02:04:28.375Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/scrm-packages-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/scrm-packages-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/scrm-packages-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/scrm-packages-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T10:28:47.173Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10798,
    description: "\u6709\u8D5E SCRM B \u7AEF\u53D1\u5E03\u4ED3\u5E93\u3002\r\n\u6CE8\u610F\uFF1A\u4E0D\u662F\u6E90\u7801\u4ED3\u5E93\uFF0C\u4EC5\u7528\u4E8E\u53D1\u5E03\u3002",
    name: "scrm-b-pc-dist",
    name_with_namespace: "fe / scrm-b-pc-dist",
    path: "scrm-b-pc-dist",
    path_with_namespace: "fe/scrm-b-pc-dist",
    created_at: "2020-10-29T07:31:27.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/scrm-b-pc-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/scrm-b-pc-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/scrm-b-pc-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T10:24:33.509Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4947,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6982\u51B5\u3001\u5E97\u94FA",
    name: "wsc-pc-shop",
    name_with_namespace: "wsc-node / wsc-pc-shop",
    path: "wsc-pc-shop",
    path_with_namespace: "wsc-node/wsc-pc-shop",
    created_at: "2018-06-11T03:07:57.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-shop.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-shop/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 7,
    last_activity_at: "2025-07-11T10:06:12.352Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14787,
    description: "",
    name: "Ci Cache",
    name_with_namespace: "fe / Ci Cache",
    path: "ci_cache",
    path_with_namespace: "fe/ci_cache",
    created_at: "2023-08-29T08:54:43.183Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ci_cache.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ci_cache.git",
    web_url: "https://gitlab.qima-inc.com/fe/ci_cache",
    readme_url: "https://gitlab.qima-inc.com/fe/ci_cache/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T09:53:33.282Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10775,
    description: "\u6709\u8D5E SCRM monorepo",
    name: "scrm-mono",
    name_with_namespace: "fe / scrm-mono",
    path: "scrm-mono",
    path_with_namespace: "fe/scrm-mono",
    created_at: "2020-10-27T09:43:31.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/scrm-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/scrm-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/scrm-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/scrm-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-11T09:51:01.617Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4585,
    description: "The multi package repo for IM project",
    name: "im-web",
    name_with_namespace: "fe / im-web",
    path: "im-web",
    path_with_namespace: "fe/im-web",
    created_at: "2018-04-18T06:47:30.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/im-web.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/im-web.git",
    web_url: "https://gitlab.qima-inc.com/fe/im-web",
    readme_url: "https://gitlab.qima-inc.com/fe/im-web/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-11T09:48:58.847Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15150,
    description: "\u79C1\u57DF\u76F4\u64ADext",
    name: "ext-tee-live",
    name_with_namespace: "private-live / ext-tee-live",
    path: "ext-tee-live",
    path_with_namespace: "private-live/ext-tee-live",
    created_at: "2024-12-26T03:09:02.372Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:private-live/ext-tee-live.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/private-live/ext-tee-live.git",
    web_url: "https://gitlab.qima-inc.com/private-live/ext-tee-live",
    readme_url: "https://gitlab.qima-inc.com/private-live/ext-tee-live/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T09:10:01.776Z",
    namespace: {
      id: 3840,
      name: "private-live",
      path: "private-live",
      kind: "group",
      full_path: "private-live",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/private-live"
    }
  },
  {
    id: 10567,
    description: "",
    name: "decorate-tee",
    name_with_namespace: "weapp / decorate-tee",
    path: "decorate-tee",
    path_with_namespace: "weapp/decorate-tee",
    created_at: "2020-09-15T12:44:03.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/decorate-tee.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/decorate-tee.git",
    web_url: "https://gitlab.qima-inc.com/weapp/decorate-tee",
    readme_url: "https://gitlab.qima-inc.com/weapp/decorate-tee/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-11T08:53:13.443Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 14921,
    description: "",
    name: "fe-test-service",
    name_with_namespace: "fe-middle-platform / fe-test-service",
    path: "fe-test-service",
    path_with_namespace: "fe-middle-platform/fe-test-service",
    created_at: "2024-03-05T08:02:20.473Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/fe-test-service.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/fe-test-service/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T08:29:13.788Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 11628,
    description: "\u5FAE\u5546\u57CE\u88C5\u4FEE  - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-wsc-decorate",
    name_with_namespace: "weapp / ext-tee-wsc-decorate",
    path: "ext-tee-wsc-decorate",
    path_with_namespace: "weapp/ext-tee-wsc-decorate",
    created_at: "2021-03-09T02:50:23.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-wsc-decorate.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-decorate/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T08:26:21.465Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 12207,
    description: "\u8D27\u67B6\u5C0F\u7A0B\u5E8F\u8DE8\u7AEF\u4ED3\u5E93",
    name: "ext-tee-retail-shelf",
    name_with_namespace: "retail-web / retail-tee / ext-tee-retail-shelf",
    path: "ext-tee-retail-shelf",
    path_with_namespace: "retail-web/retail-tee/ext-tee-retail-shelf",
    created_at: "2021-06-04T08:29:50.598Z",
    default_branch: "dev",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-tee/ext-tee-retail-shelf.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-tee/ext-tee-retail-shelf/-/blob/dev/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-11T08:24:33.817Z",
    namespace: {
      id: 2872,
      name: "retail-tee",
      path: "retail-tee",
      kind: "group",
      full_path: "retail-web/retail-tee",
      parent_id: 579,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/retail-web/retail-tee"
    }
  },
  {
    id: 10755,
    description: "\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEF monorepo",
    name: "weass-b-mono",
    name_with_namespace: "fe / weass-b-mono",
    path: "weass-b-mono",
    path_with_namespace: "fe/weass-b-mono",
    created_at: "2020-10-23T11:01:07.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-b-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-b-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-b-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/weass-b-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-11T08:22:27.003Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5182,
    description: "\u5FAE\u5546\u57CE\u6D88\u606F\u76F8\u5173\u4E1A\u52A1\u6E90\u7801\u4ED3\u5E93",
    name: "wsc-pc-message-web",
    name_with_namespace: "fe / wsc-pc-message-web",
    path: "wsc-pc-message-web",
    path_with_namespace: "fe/wsc-pc-message-web",
    created_at: "2018-07-16T12:27:17.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/wsc-pc-message-web.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/wsc-pc-message-web.git",
    web_url: "https://gitlab.qima-inc.com/fe/wsc-pc-message-web",
    readme_url: "https://gitlab.qima-inc.com/fe/wsc-pc-message-web/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-11T08:21:11.263Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14566,
    description: "",
    name: "jarvis-front",
    name_with_namespace: "fe / jarvis-front",
    path: "jarvis-front",
    path_with_namespace: "fe/jarvis-front",
    created_at: "2023-03-20T10:04:36.182Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/jarvis-front.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/jarvis-front.git",
    web_url: "https://gitlab.qima-inc.com/fe/jarvis-front",
    readme_url: "https://gitlab.qima-inc.com/fe/jarvis-front/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T08:15:40.876Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12951,
    description: "\u6709\u8D5E\u4E91\u6587\u6863\u4E2D\u5FC3\u7684\u6587\u6863\u6E90\u6587\u4EF6\r\n\u8BE5\u4ED3\u5E93\u975E\u6709\u8D5E\u4E91\u6587\u6863\u4E2D\u5FC3\u7684\u6E90\u4EE3\u7801",
    name: "CloudDocs",
    name_with_namespace: "youzan / CloudDocs",
    path: "cloudDocs",
    path_with_namespace: "youzan/cloudDocs",
    created_at: "2021-10-09T07:35:56.865Z",
    default_branch: "preview",
    tag_list: [],
    ssh_url_to_repo: "***********************:youzan/cloudDocs.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/youzan/cloudDocs.git",
    web_url: "https://gitlab.qima-inc.com/youzan/cloudDocs",
    readme_url: "https://gitlab.qima-inc.com/youzan/cloudDocs/-/blob/preview/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T08:11:11.697Z",
    namespace: {
      id: 5,
      name: "youzan",
      path: "youzan",
      kind: "group",
      full_path: "youzan",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/5/cda5c81e0c64c791c4c190c1efcc1eaa.png",
      web_url: "https://gitlab.qima-inc.com/groups/youzan"
    }
  },
  {
    id: 9988,
    description: "\u88C5\u4FEE\u76F8\u5173\u4E1A\u52A1",
    name: "wsc-h5-decorate",
    name_with_namespace: "wsc-node / wsc-h5-decorate",
    path: "wsc-h5-decorate",
    path_with_namespace: "wsc-node/wsc-h5-decorate",
    created_at: "2020-07-06T09:26:12.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-decorate.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-decorate/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-11T08:01:19.422Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 15198,
    description: "",
    name: "ai-test",
    name_with_namespace: "fe / ai-test",
    path: "ai-test",
    path_with_namespace: "fe/ai-test",
    created_at: "2025-03-03T03:20:44.577Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ai-test.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ai-test.git",
    web_url: "https://gitlab.qima-inc.com/fe/ai-test",
    readme_url: "https://gitlab.qima-inc.com/fe/ai-test/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T08:00:26.005Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6796,
    description: "",
    name: "wsc-h5-showcase-components",
    name_with_namespace: "fe / wsc-h5-showcase-components",
    path: "wsc-h5-showcase-components",
    path_with_namespace: "fe/wsc-h5-showcase-components",
    created_at: "2019-02-28T09:57:05.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/wsc-h5-showcase-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components.git",
    web_url: "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components",
    readme_url: "https://gitlab.qima-inc.com/fe/wsc-h5-showcase-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-11T07:55:21.029Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15200,
    description: "",
    name: "ai-test-chrome-extension",
    name_with_namespace: "fe / ai-test-chrome-extension",
    path: "ai-test-chrome-extension",
    path_with_namespace: "fe/ai-test-chrome-extension",
    created_at: "2025-03-03T10:55:02.824Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ai-test-chrome-extension.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension.git",
    web_url: "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension",
    readme_url: "https://gitlab.qima-inc.com/fe/ai-test-chrome-extension/-/blob/main/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/15200/20250319-173543.png",
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T07:54:16.638Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14597,
    description: "",
    name: "*********************",
    name_with_namespace: "fe / *********************",
    path: "*********************",
    path_with_namespace: "fe/*********************",
    created_at: "2023-04-19T09:16:07.046Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/*********************.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/*********************.git",
    web_url: "https://gitlab.qima-inc.com/fe/*********************",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T07:41:21.045Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14589,
    description: "",
    name: "jarvis-commander-mono",
    name_with_namespace: "fe / jarvis-commander-mono",
    path: "jarvis-commander-mono",
    path_with_namespace: "fe/jarvis-commander-mono",
    created_at: "2023-04-13T02:19:27.964Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/jarvis-commander-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/jarvis-commander-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/jarvis-commander-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/jarvis-commander-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T07:33:31.326Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4576,
    description: "Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5E94\u7528\u8425\u9500",
    name: "wsc-h5-ump",
    name_with_namespace: "wsc-node / wsc-h5-ump",
    path: "wsc-h5-ump",
    path_with_namespace: "wsc-node/wsc-h5-ump",
    created_at: "2018-04-17T07:10:05.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-ump.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-ump/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-11T07:30:44.295Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14448,
    description: "\u8425\u9500\u753B\u5E03\u524D\u7AEF\u4ED3\u5E93",
    name: "ma-front",
    name_with_namespace: "fe / ma-front",
    path: "ma-front",
    path_with_namespace: "fe/ma-front",
    created_at: "2022-11-30T06:02:24.470Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ma-front.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ma-front.git",
    web_url: "https://gitlab.qima-inc.com/fe/ma-front",
    readme_url: "https://gitlab.qima-inc.com/fe/ma-front/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/14448/00000105.jpg",
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T07:26:44.775Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11520,
    description: "",
    name: "guide-b-h5",
    name_with_namespace: "wsc-node / guide-b-h5",
    path: "guide-b-h5",
    path_with_namespace: "wsc-node/guide-b-h5",
    created_at: "2021-02-19T03:58:17.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/guide-b-h5.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/guide-b-h5.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/guide-b-h5",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/guide-b-h5/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-11T07:18:43.591Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 4555,
    description: "Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5FAE\u9875\u9762\u3001\u5546\u54C1\u3001\u5E97\u94FA",
    name: "wsc-h5-shop",
    name_with_namespace: "wsc-node / wsc-h5-shop",
    path: "wsc-h5-shop",
    path_with_namespace: "wsc-node/wsc-h5-shop",
    created_at: "2018-04-13T02:22:55.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-shop.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shop/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 1,
    star_count: 8,
    last_activity_at: "2025-07-11T07:16:16.403Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 4573,
    description: "Iron \u62C6\u5206\u4E1A\u52A1\uFF1A\u4EA4\u6613\u3001\u4E0B\u5355\u3001\u8BA2\u5355",
    name: "wsc-h5-trade",
    name_with_namespace: "wsc-node / wsc-h5-trade",
    path: "wsc-h5-trade",
    path_with_namespace: "wsc-node/wsc-h5-trade",
    created_at: "2018-04-17T03:20:29.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-trade.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-trade/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/4573/Node.js__2_.png",
    forks_count: 0,
    star_count: 13,
    last_activity_at: "2025-07-11T07:01:47.350Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 8911,
    description: "\u5206\u9500\u5458\uFF0C\u4E91\u5206\u9500 \u6D88\u8D39\u8005\u7AEF\u4E1A\u52A1",
    name: "wsc-h5-salesman",
    name_with_namespace: "wsc-node / wsc-h5-salesman",
    path: "wsc-h5-salesman",
    path_with_namespace: "wsc-node/wsc-h5-salesman",
    created_at: "2020-01-17T03:51:32.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-salesman.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-salesman/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-11T06:53:45.475Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 5046,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1ASCRM",
    name: "wsc-pc-scrm",
    name_with_namespace: "wsc-node / wsc-pc-scrm",
    path: "wsc-pc-scrm",
    path_with_namespace: "wsc-node/wsc-pc-scrm",
    created_at: "2018-06-27T07:40:55.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-scrm.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-scrm/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-11T06:44:15.482Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 1979,
    description: "\u5FAE\u5546\u57CEReact\u4E1A\u52A1\u7EC4\u4EF6",
    name: "react-components",
    name_with_namespace: "fe / react-components",
    path: "react-components",
    path_with_namespace: "fe/react-components",
    created_at: "2017-02-21T09:36:48.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/react-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/react-components.git",
    web_url: "https://gitlab.qima-inc.com/fe/react-components",
    readme_url: "https://gitlab.qima-inc.com/fe/react-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 23,
    last_activity_at: "2025-07-11T06:27:31.161Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11992,
    description: "\u5BFC\u8D2DB\u7AEFpc",
    name: "guide-b-pc",
    name_with_namespace: "fe / guide-b-pc",
    path: "guide-b-pc",
    path_with_namespace: "fe/guide-b-pc",
    created_at: "2021-04-26T13:29:38.263Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/guide-b-pc.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/guide-b-pc.git",
    web_url: "https://gitlab.qima-inc.com/fe/guide-b-pc",
    readme_url: "https://gitlab.qima-inc.com/fe/guide-b-pc/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-11T06:23:50.824Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15222,
    description: "\u4ECE github midscene https://github.com/web-infra-dev/midscene  \u5206\u53C9\u8FC7\u6765\u7684",
    name: "midscene",
    name_with_namespace: "fe / midscene",
    path: "midscene",
    path_with_namespace: "fe/midscene",
    created_at: "2025-03-06T08:06:25.682Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/midscene.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/midscene.git",
    web_url: "https://gitlab.qima-inc.com/fe/midscene",
    readme_url: "https://gitlab.qima-inc.com/fe/midscene/-/blob/main/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T06:19:12.365Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 3780,
    description: "\u5FAE\u9875\u9762\u6742\u5FD7\u8001\u6570\u636E-\u65B0\u6570\u636E-captain\u7EC4\u4EF6\u7684\u8F6C\u5316\u51FD\u6570",
    name: "feature-adaptor",
    name_with_namespace: "fe / feature-adaptor",
    path: "feature-adaptor",
    path_with_namespace: "fe/feature-adaptor",
    created_at: "2017-11-11T09:05:08.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/feature-adaptor.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/feature-adaptor.git",
    web_url: "https://gitlab.qima-inc.com/fe/feature-adaptor",
    readme_url: "https://gitlab.qima-inc.com/fe/feature-adaptor/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T06:16:48.013Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 9989,
    description: "",
    name: "wsc-pc-decorate",
    name_with_namespace: "wsc-node / wsc-pc-decorate",
    path: "wsc-pc-decorate",
    path_with_namespace: "wsc-node/wsc-pc-decorate",
    created_at: "2020-07-06T09:26:49.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-decorate.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-decorate/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-11T06:12:15.096Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 15232,
    description: "",
    name: "ai-data",
    name_with_namespace: "fe / ai-data",
    path: "ai-data",
    path_with_namespace: "fe/ai-data",
    created_at: "2025-03-11T11:57:03.949Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ai-data.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ai-data.git",
    web_url: "https://gitlab.qima-inc.com/fe/ai-data",
    readme_url: "https://gitlab.qima-inc.com/fe/ai-data/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T06:09:16.489Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14209,
    description: "ranta \u517C\u5BB9\u8001\u5B9A\u5236",
    name: "ranta-yun-adapter",
    name_with_namespace: "fe-middle-platform / ranta-yun-adapter",
    path: "ranta-yun-adapter",
    path_with_namespace: "fe-middle-platform/ranta-yun-adapter",
    created_at: "2022-05-27T03:33:41.661Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-yun-adapter.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-yun-adapter/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T04:06:41.784Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 14891,
    description: "\u52A0\u6211\u667A\u80FD\u524D\u7AEF\u653E\u5728\u5FAE\u5546\u57CE\u5185\u7684\u9875\u9762",
    name: "wsc-pc-jiawo",
    name_with_namespace: "zanai / wsc-pc-jiawo",
    path: "wsc-pc-jiawo",
    path_with_namespace: "zanai/wsc-pc-jiawo",
    created_at: "2024-01-22T02:26:06.877Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:zanai/wsc-pc-jiawo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo.git",
    web_url: "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo",
    readme_url: "https://gitlab.qima-inc.com/zanai/wsc-pc-jiawo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T03:48:55.878Z",
    namespace: {
      id: 3740,
      name: "zanai",
      path: "zanai",
      kind: "group",
      full_path: "zanai",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/zanai"
    }
  },
  {
    id: 4952,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6570\u636E",
    name: "wsc-pc-statcenter",
    name_with_namespace: "wsc-node / wsc-pc-statcenter",
    path: "wsc-pc-statcenter",
    path_with_namespace: "wsc-node/wsc-pc-statcenter",
    created_at: "2018-06-11T03:26:10.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-statcenter.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-statcenter/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T03:45:08.722Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14974,
    description: "",
    name: "garden-echo",
    name_with_namespace: "fe / garden-echo",
    path: "garden-echo",
    path_with_namespace: "fe/garden-echo",
    created_at: "2024-05-22T09:52:32.756Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-echo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-echo.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-echo",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-echo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T03:43:39.964Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15401,
    description: "\u4E2D\u53F0\u5316 H5 \u5546\u5BB6\u7AEF",
    name: "admin-tee-h5-app",
    name_with_namespace: "fe / admin-tee-h5-app",
    path: "admin-tee-h5-app",
    path_with_namespace: "fe/admin-tee-h5-app",
    created_at: "2025-06-12T07:15:43.446Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/admin-tee-h5-app.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/admin-tee-h5-app.git",
    web_url: "https://gitlab.qima-inc.com/fe/admin-tee-h5-app",
    readme_url: "https://gitlab.qima-inc.com/fe/admin-tee-h5-app/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T02:50:27.063Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10757,
    description: "\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEFPC\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",
    name: "weass-b-pc-dist",
    name_with_namespace: "fe / weass-b-pc-dist",
    path: "weass-b-pc-dist",
    path_with_namespace: "fe/weass-b-pc-dist",
    created_at: "2020-10-23T11:04:37.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-b-pc-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-b-pc-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-b-pc-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T02:47:08.375Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 8648,
    description: "\u5206\u9500\u5458\uFF0C\u4E91\u5206\u9500 \u5546\u5BB6\u8005\u7AEF\u4E1A\u52A1",
    name: "wsc-pc-salesman",
    name_with_namespace: "wsc-node / wsc-pc-salesman",
    path: "wsc-pc-salesman",
    path_with_namespace: "wsc-node/wsc-pc-salesman",
    created_at: "2019-11-28T01:21:32.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-salesman.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-salesman/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-11T02:38:23.024Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14318,
    description: "",
    name: "wsc-tee-goods-common",
    name_with_namespace: "fe / wsc-tee-goods-common",
    path: "wsc-goods-common",
    path_with_namespace: "fe/wsc-goods-common",
    created_at: "2022-08-16T02:38:25.500Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/wsc-goods-common.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/wsc-goods-common.git",
    web_url: "https://gitlab.qima-inc.com/fe/wsc-goods-common",
    readme_url: "https://gitlab.qima-inc.com/fe/wsc-goods-common/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T02:13:21.262Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4946,
    description: "Iron PC \u57FA\u7840\u4E1A\u52A1\u6846\u67B6",
    name: "wsc-pc-base",
    name_with_namespace: "wsc-node / wsc-pc-base",
    path: "wsc-pc-base",
    path_with_namespace: "wsc-node/wsc-pc-base",
    created_at: "2018-06-11T03:03:49.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-base.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-base/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 7,
    last_activity_at: "2025-07-11T00:20:02.191Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 12783,
    description: "ungoro \u9879\u76EE\u7684\u90E8\u7F72\u4ED3\u5E93",
    name: "ungoro-dist",
    name_with_namespace: "fe / ungoro-dist",
    path: "ungoro-dist",
    path_with_namespace: "fe/ungoro-dist",
    created_at: "2021-09-07T11:17:03.847Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ungoro-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ungoro-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/ungoro-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-11T00:10:06.041Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12680,
    description: "\u8FDB\u5E97\u670D\u52A1",
    name: "Enter Shop Service",
    name_with_namespace: "fe / Enter Shop Service",
    path: "enter-shop-service",
    path_with_namespace: "fe/enter-shop-service",
    created_at: "2021-08-23T07:49:35.270Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/enter-shop-service.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/enter-shop-service.git",
    web_url: "https://gitlab.qima-inc.com/fe/enter-shop-service",
    readme_url: "https://gitlab.qima-inc.com/fe/enter-shop-service/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T11:33:39.028Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 7388,
    description: "Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5546\u54C1",
    name: "wsc-h5-goods",
    name_with_namespace: "wsc-node / wsc-h5-goods",
    path: "wsc-h5-goods",
    path_with_namespace: "wsc-node/wsc-h5-goods",
    created_at: "2019-05-23T06:54:33.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-goods.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-goods/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-10T11:18:05.942Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 11586,
    description: "Ranta \u7F16\u8BD1\u65F6\u63D2\u4EF6",
    name: "ranta-compiler",
    name_with_namespace: "fe-middle-platform / ranta-compiler",
    path: "ranta-compiler",
    path_with_namespace: "fe-middle-platform/ranta-compiler",
    created_at: "2021-03-02T09:02:43.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-compiler.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-compiler/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-10T09:17:18.137Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 12139,
    description: "\u96F6\u552E\u8DE8\u7AEF\u901A\u7528\u4ED3\u5E93",
    name: "retail-tee-common",
    name_with_namespace: "retail-web / retail-tee / retail-tee-common",
    path: "retail-tee-common",
    path_with_namespace: "retail-web/retail-tee/retail-tee-common",
    created_at: "2021-05-24T09:09:28.906Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-tee/retail-tee-common.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-tee/retail-tee-common/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T09:13:16.245Z",
    namespace: {
      id: 2872,
      name: "retail-tee",
      path: "retail-tee",
      kind: "group",
      full_path: "retail-web/retail-tee",
      parent_id: 579,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/retail-web/retail-tee"
    }
  },
  {
    id: 7523,
    description: "\u5C0F\u7A0B\u5E8F\u7BA1\u7406\u5E73\u53F0",
    name: "weapp-manager",
    name_with_namespace: "weapp / weapp-manager",
    path: "weapp-manager",
    path_with_namespace: "weapp/weapp-manager",
    created_at: "2019-06-11T13:37:59.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/weapp-manager.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/weapp-manager.git",
    web_url: "https://gitlab.qima-inc.com/weapp/weapp-manager",
    readme_url: "https://gitlab.qima-inc.com/weapp/weapp-manager/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-10T08:41:04.150Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 4951,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u8425\u9500",
    name: "wsc-pc-ump",
    name_with_namespace: "wsc-node / wsc-pc-ump",
    path: "wsc-pc-ump",
    path_with_namespace: "wsc-node/wsc-pc-ump",
    created_at: "2018-06-11T03:21:53.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-ump.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-ump/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 12,
    last_activity_at: "2025-07-10T08:37:00.038Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 13965,
    description: "",
    name: "crm-c-h5-dist",
    name_with_namespace: "fe / crm-c-h5-dist",
    path: "crm-c-h5-dist",
    path_with_namespace: "fe/crm-c-h5-dist",
    created_at: "2022-03-14T02:23:37.999Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/crm-c-h5-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/crm-c-h5-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/crm-c-h5-dist",
    readme_url: "https://gitlab.qima-inc.com/fe/crm-c-h5-dist/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T08:08:23.465Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15033,
    description: "",
    name: "order-domain",
    name_with_namespace: "retail-web / order-domain",
    path: "order-domain",
    path_with_namespace: "retail-web/order-domain",
    created_at: "2024-08-13T11:13:51.445Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/order-domain.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/order-domain.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/order-domain",
    readme_url: "https://gitlab.qima-inc.com/retail-web/order-domain/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T07:16:27.911Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 10635,
    description: "Tee \u5927\u6570\u636E\u5B50\u4ED3\u5E93",
    name: "wsc-tee-statcenter",
    name_with_namespace: "weapp / wsc-tee-statcenter",
    path: "wsc-tee-statcenter",
    path_with_namespace: "weapp/wsc-tee-statcenter",
    created_at: "2020-09-27T07:43:51.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-statcenter.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-statcenter/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T06:59:28.701Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 5870,
    description: "",
    name: "pc-shared-service",
    name_with_namespace: "wsc-node / pc-shared-service",
    path: "pc-shared-service",
    path_with_namespace: "wsc-node/pc-shared-service",
    created_at: "2018-10-22T08:44:14.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/pc-shared-service.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/pc-shared-service.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/pc-shared-service",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/pc-shared-service/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-10T06:46:15.853Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6372,
    description: "pc-shared-service\u6E90\u7801\u76EE\u5F55",
    name: "pc-shared-service-source",
    name_with_namespace: "fe / pc-shared-service-source",
    path: "pc-shared-service-source",
    path_with_namespace: "fe/pc-shared-service-source",
    created_at: "2018-12-26T05:58:06.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/pc-shared-service-source.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/pc-shared-service-source.git",
    web_url: "https://gitlab.qima-inc.com/fe/pc-shared-service-source",
    readme_url: "https://gitlab.qima-inc.com/fe/pc-shared-service-source/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 1,
    star_count: 9,
    last_activity_at: "2025-07-10T06:38:13.035Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11447,
    description: "\u7D20\u6750\u7BA1\u7406",
    name: "wsc-materials",
    name_with_namespace: "wsc-node / wsc-materials",
    path: "wsc-materials",
    path_with_namespace: "wsc-node/wsc-materials",
    created_at: "2021-02-03T03:44:53.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-materials.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-materials.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-materials",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-materials/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-10T02:44:50.407Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14812,
    description: "",
    name: "ext-tee-user",
    name_with_namespace: "weapp / ext-tee-user",
    path: "ext-tee-user",
    path_with_namespace: "weapp/ext-tee-user",
    created_at: "2023-09-22T09:53:56.573Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-user.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-user.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-user",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-user/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-10T02:11:34.029Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11543,
    description: "\u57FA\u4E8Ezent\u7684PC\u4E1A\u52A1\u901A\u7528\u4EA4\u4E92\u6A21\u5F0F",
    name: "zent-pattern",
    name_with_namespace: "fe / zent-pattern",
    path: "zent-pattern",
    path_with_namespace: "fe/zent-pattern",
    created_at: "2021-02-23T02:13:49.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/zent-pattern.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/zent-pattern.git",
    web_url: "https://gitlab.qima-inc.com/fe/zent-pattern",
    readme_url: "https://gitlab.qima-inc.com/fe/zent-pattern/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T09:28:49.135Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4592,
    description: "Iron H5 \u62C6\u5206\u4E1A\u52A1\uFF1A\u5404\u79CD\u6742\u4E03\u6742\u516B\u4E1A\u52A1",
    name: "wsc-h5-v3",
    name_with_namespace: "wsc-node / wsc-h5-v3",
    path: "wsc-h5-v3",
    path_with_namespace: "wsc-node/wsc-h5-v3",
    created_at: "2018-04-19T03:26:13.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-v3.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-v3/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-09T07:54:46.046Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 9617,
    description: "\u4E09\u65B9\u6E20\u9053",
    name: "wsc-pc-channel",
    name_with_namespace: "wsc-node / wsc-pc-channel",
    path: "wsc-pc-channel",
    path_with_namespace: "wsc-node/wsc-pc-channel",
    created_at: "2020-05-18T02:53:27.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-channel.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-channel/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T07:38:36.417Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 4948,
    description: "Iron PC \u62C6\u5206\u4E1A\u52A1\uFF1A\u6742\u4E03\u6742\u516B\u4E1A\u52A1\uFF0C\u8BBE\u7F6E",
    name: "wsc-pc-v4",
    name_with_namespace: "wsc-node / wsc-pc-v4",
    path: "wsc-pc-v4",
    path_with_namespace: "wsc-node/wsc-pc-v4",
    created_at: "2018-06-11T03:10:04.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-v4.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-v4/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 7,
    last_activity_at: "2025-07-09T07:19:06.577Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 12947,
    description: "",
    name: "salesman-mono",
    name_with_namespace: "fe / salesman-mono",
    path: "salesman-mono",
    path_with_namespace: "fe/salesman-mono",
    created_at: "2021-10-08T11:54:41.260Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/salesman-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/salesman-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/salesman-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/salesman-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-09T07:05:20.843Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11814,
    description: "",
    name: "wsc-tee-salesman",
    name_with_namespace: "weapp / wsc-tee-salesman",
    path: "wsc-tee-salesman",
    path_with_namespace: "weapp/wsc-tee-salesman",
    created_at: "2021-04-01T09:41:17.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-salesman.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-salesman/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-09T07:05:07.522Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 5902,
    description: "App \u5E94\u7528\u5E02\u573A app.youzanyun.com",
    name: "app-web",
    name_with_namespace: "fe-ecloud / app-web",
    path: "app-web",
    path_with_namespace: "fe-ecloud/app-web",
    created_at: "2018-10-29T07:38:04.000Z",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-ecloud/app-web.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-ecloud/app-web.git",
    web_url: "https://gitlab.qima-inc.com/fe-ecloud/app-web",
    readme_url: "https://gitlab.qima-inc.com/fe-ecloud/app-web/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-09T06:14:40.171Z",
    namespace: {
      id: 1334,
      name: "fe-ecloud",
      path: "fe-ecloud",
      kind: "group",
      full_path: "fe-ecloud",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/fe-ecloud"
    }
  },
  {
    id: 4700,
    description: "The distribution of im-node.",
    name: "im-node-dist",
    name_with_namespace: "fe / im-node-dist",
    path: "im-node-dist",
    path_with_namespace: "fe/im-node-dist",
    created_at: "2018-05-09T03:03:36.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/im-node-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/im-node-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/im-node-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T06:11:30.514Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6999,
    description: "C\u7AEFIM\u53D1\u5E03\u4ED3\u5E93",
    name: "wap-im-dist",
    name_with_namespace: "fe / wap-im-dist",
    path: "wap-im-dist",
    path_with_namespace: "fe/wap-im-dist",
    created_at: "2019-04-03T02:04:26.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/wap-im-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/wap-im-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/wap-im-dist",
    readme_url: "https://gitlab.qima-inc.com/fe/wap-im-dist/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T03:59:02.419Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6998,
    description: "C\u7AEFIM\u6E90\u7801\u4ED3\u5E93",
    name: "wap-im-web",
    name_with_namespace: "fe / wap-im-web",
    path: "wap-im-web",
    path_with_namespace: "fe/wap-im-web",
    created_at: "2019-04-03T02:03:49.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/wap-im-web.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/wap-im-web.git",
    web_url: "https://gitlab.qima-inc.com/fe/wap-im-web",
    readme_url: "https://gitlab.qima-inc.com/fe/wap-im-web/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-09T03:55:53.901Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14634,
    description: "",
    name: "cloud-ranta-tee",
    name_with_namespace: "fe-middle-platform / cloud-ranta-tee",
    path: "cloud-ranta-tee",
    path_with_namespace: "fe-middle-platform/cloud-ranta-tee",
    created_at: "2023-05-08T02:48:30.979Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/cloud-ranta-tee.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/cloud-ranta-tee/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T03:43:21.151Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 12767,
    description: "CRM \u79DF\u6237\u6807\u5904\u7406",
    name: "platform-tenant",
    name_with_namespace: "fe / platform-tenant",
    path: "platform-tenant",
    path_with_namespace: "fe/platform-tenant",
    created_at: "2021-09-06T03:47:34.991Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/platform-tenant.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/platform-tenant.git",
    web_url: "https://gitlab.qima-inc.com/fe/platform-tenant",
    readme_url: "https://gitlab.qima-inc.com/fe/platform-tenant/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T03:31:35.799Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10934,
    description: "\u6709\u8D5E scrm \u5E97\u94FA\u5347\u7EA7\u68C0\u67E5\u63D2\u4EF6",
    name: "scrm-shop-check-plugin",
    name_with_namespace: "fe / scrm-shop-check-plugin",
    path: "scrm-shop-check-plugin",
    path_with_namespace: "fe/scrm-shop-check-plugin",
    created_at: "2020-11-20T04:07:04.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/scrm-shop-check-plugin.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin.git",
    web_url: "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin",
    readme_url: "https://gitlab.qima-inc.com/fe/scrm-shop-check-plugin/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T03:29:06.187Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6321,
    description: "",
    name: "trash",
    name_with_namespace: "chenting / trash",
    path: "trash",
    path_with_namespace: "chenting/trash",
    created_at: "2018-12-18T18:23:00.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:chenting/trash.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/chenting/trash.git",
    web_url: "https://gitlab.qima-inc.com/chenting/trash",
    readme_url: "https://gitlab.qima-inc.com/chenting/trash/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-09T02:46:33.795Z",
    namespace: {
      id: 904,
      name: "chenting",
      path: "chenting",
      kind: "user",
      full_path: "chenting",
      parent_id: null,
      avatar_url: "/uploads/-/system/user/avatar/807/avatar.png",
      web_url: "https://gitlab.qima-inc.com/chenting"
    }
  },
  {
    id: 9467,
    description: "",
    name: "wsc-pc-apps",
    name_with_namespace: "fe-ecloud / wsc-pc-apps",
    path: "wsc-pc-apps",
    path_with_namespace: "fe-ecloud/wsc-pc-apps",
    created_at: "2020-04-26T11:29:29.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-ecloud/wsc-pc-apps.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps.git",
    web_url: "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps",
    readme_url: "https://gitlab.qima-inc.com/fe-ecloud/wsc-pc-apps/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-09T02:20:03.889Z",
    namespace: {
      id: 1334,
      name: "fe-ecloud",
      path: "fe-ecloud",
      kind: "group",
      full_path: "fe-ecloud",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/fe-ecloud"
    }
  },
  {
    id: 14975,
    description: "",
    name: "echo-manage",
    name_with_namespace: "fe / echo-manage",
    path: "echo-manage",
    path_with_namespace: "fe/echo-manage",
    created_at: "2024-05-23T02:31:29.417Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/echo-manage.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/echo-manage.git",
    web_url: "https://gitlab.qima-inc.com/fe/echo-manage",
    readme_url: "https://gitlab.qima-inc.com/fe/echo-manage/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-09T02:14:59.731Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 7832,
    description: "Ranta \u6C99\u7BB1",
    name: "ranta-sandbox",
    name_with_namespace: "fe-middle-platform / ranta-sandbox",
    path: "ranta-sandbox",
    path_with_namespace: "fe-middle-platform/ranta-sandbox",
    created_at: "2019-07-24T07:45:37.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-sandbox.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-sandbox/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-08T12:03:21.241Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 11220,
    description: "\u4F01\u52A9\u6D77\u62A5\u670D\u52A1\u90E8\u7F72\u4ED3\u5E93",
    name: "weass-snapshot-dist",
    name_with_namespace: "fe / weass-snapshot-dist",
    path: "weass-snapshot-dist",
    path_with_namespace: "fe/weass-snapshot-dist",
    created_at: "2021-01-04T09:57:23.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-snapshot-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-snapshot-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-snapshot-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T12:02:07.670Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10756,
    description: "\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BC\u7AEF monorepo",
    name: "weass-c-mono",
    name_with_namespace: "fe / weass-c-mono",
    path: "weass-c-mono",
    path_with_namespace: "fe/weass-c-mono",
    created_at: "2020-10-23T11:02:13.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-c-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-c-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-c-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/weass-c-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T11:59:35.331Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10758,
    description: "\u4F01\u4E1A\u5FAE\u4FE1\u52A9\u624BB\u7AEFH5\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",
    name: "weass-b-h5-dist",
    name_with_namespace: "fe / weass-b-h5-dist",
    path: "weass-b-h5-dist",
    path_with_namespace: "fe/weass-b-h5-dist",
    created_at: "2020-10-23T11:13:14.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-b-h5-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-b-h5-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-b-h5-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T07:15:59.143Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10967,
    description: "",
    name: "garden",
    name_with_namespace: "fe / garden",
    path: "garden",
    path_with_namespace: "fe/garden",
    created_at: "2020-11-26T03:03:50.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden",
    readme_url: "https://gitlab.qima-inc.com/fe/garden/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-08T07:15:34.331Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15027,
    description: "",
    name: "garden-ipaas",
    name_with_namespace: "fe / garden-ipaas",
    path: "garden-ipaas",
    path_with_namespace: "fe/garden-ipaas",
    created_at: "2024-08-07T08:12:42.138Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-ipaas.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-ipaas.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-ipaas",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-ipaas/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T06:59:56.578Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12279,
    description: "",
    name: "goods-domain",
    name_with_namespace: "fe / goods-domain",
    path: "goods-domain",
    path_with_namespace: "fe/goods-domain",
    created_at: "2021-06-17T11:40:12.044Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/goods-domain.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/goods-domain.git",
    web_url: "https://gitlab.qima-inc.com/fe/goods-domain",
    readme_url: "https://gitlab.qima-inc.com/fe/goods-domain/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-08T06:31:14.943Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12519,
    description: "",
    name: "Taurus Front",
    name_with_namespace: "fe / Taurus Front",
    path: "taurus-front",
    path_with_namespace: "fe/taurus-front",
    created_at: "2021-07-26T06:09:01.402Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/taurus-front.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/taurus-front.git",
    web_url: "https://gitlab.qima-inc.com/fe/taurus-front",
    readme_url: "https://gitlab.qima-inc.com/fe/taurus-front/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T03:24:15.309Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 9985,
    description: "",
    name: "wsc-h5-statcenter",
    name_with_namespace: "wsc-node / wsc-h5-statcenter",
    path: "wsc-h5-statcenter",
    path_with_namespace: "wsc-node/wsc-h5-statcenter",
    created_at: "2020-07-06T07:29:26.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-statcenter.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-statcenter/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-08T02:18:12.111Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 12843,
    description: "",
    name: "garden-gdp",
    name_with_namespace: "fe / garden-gdp",
    path: "garden-gdp",
    path_with_namespace: "fe/garden-gdp",
    created_at: "2021-09-16T13:32:45.469Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-gdp.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-gdp.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-gdp",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-gdp/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-07T12:25:38.488Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15402,
    description: "",
    name: "test",
    name_with_namespace: "xujiazheng / test",
    path: "test",
    path_with_namespace: "xujiazheng/test",
    created_at: "2025-06-13T03:23:21.547Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:xujiazheng/test.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/xujiazheng/test.git",
    web_url: "https://gitlab.qima-inc.com/xujiazheng/test",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-07T11:54:48.693Z",
    namespace: {
      id: 3020,
      name: "xujiazheng",
      path: "xujiazheng",
      kind: "user",
      full_path: "xujiazheng",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/xujiazheng"
    }
  },
  {
    id: 11535,
    description: "",
    name: "ui-test",
    name_with_namespace: "retail-web / ui-test",
    path: "ui-test",
    path_with_namespace: "retail-web/ui-test",
    created_at: "2021-02-22T02:30:40.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/ui-test.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/ui-test.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/ui-test",
    readme_url: "https://gitlab.qima-inc.com/retail-web/ui-test/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-07T09:35:40.562Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 14833,
    description: "\u65B0\u96F6\u552E\u8FD0\u8425\u5E73\u53F0",
    name: "garden-retail",
    name_with_namespace: "fe / garden-retail",
    path: "garden-retail",
    path_with_namespace: "fe/garden-retail",
    created_at: "2023-10-25T03:19:09.089Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-retail.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-retail.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-retail",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-retail/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-07T07:15:22.540Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11661,
    description: "\u8D26\u53F7 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-passport",
    name_with_namespace: "weapp / ext-tee-passport",
    path: "ext-tee-passport",
    path_with_namespace: "weapp/ext-tee-passport",
    created_at: "2021-03-12T09:37:42.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-passport.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-passport.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-passport",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-passport/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-07T03:46:51.226Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 15323,
    description: "",
    name: "jira-assistant",
    name_with_namespace: "fe / jira-assistant",
    path: "jira-assistant",
    path_with_namespace: "fe/jira-assistant",
    created_at: "2025-05-19T06:24:52.694Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/jira-assistant.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/jira-assistant.git",
    web_url: "https://gitlab.qima-inc.com/fe/jira-assistant",
    readme_url: "https://gitlab.qima-inc.com/fe/jira-assistant/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-07T02:53:21.086Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10969,
    description: "",
    name: "wsc-h5-aftersales",
    name_with_namespace: "wsc-node / wsc-h5-aftersales",
    path: "wsc-h5-aftersales",
    path_with_namespace: "wsc-node/wsc-h5-aftersales",
    created_at: "2020-11-26T05:33:00.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-aftersales.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-aftersales/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-07-06T16:00:08.908Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 12261,
    description: "\u5FAE\u4FE1\u89C6\u9891\u53F7",
    name: "wsc-pc-wxvideo",
    name_with_namespace: "wsc-node / wsc-pc-wxvideo",
    path: "wsc-pc-wxvideo",
    path_with_namespace: "wsc-node/wsc-pc-wxvideo",
    created_at: "2021-06-16T03:23:50.794Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-wxvideo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-wxvideo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T16:10:36.531Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 4422,
    description: "\u4F1A\u5458\u4E2D\u5FC3\uFF0C\u6743\u76CA\u5361\u7B49\u4F1A\u5458 C \u7AEF\u4E1A\u52A1",
    name: "wsc-h5-user",
    name_with_namespace: "wsc-node / wsc-h5-user",
    path: "wsc-h5-user",
    path_with_namespace: "wsc-node/wsc-h5-user",
    created_at: "2018-03-22T03:07:08.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-user.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-user/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-07-04T16:10:30.802Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 11626,
    description: "",
    name: "matrix-manage-node-dist",
    name_with_namespace: "fe / matrix-manage-node-dist",
    path: "matrix-manage-node-dist",
    path_with_namespace: "fe/matrix-manage-node-dist",
    created_at: "2021-03-08T12:18:27.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/matrix-manage-node-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist",
    readme_url: "https://gitlab.qima-inc.com/fe/matrix-manage-node-dist/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T16:10:29.390Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11625,
    description: "",
    name: "matrix-runtime",
    name_with_namespace: "fe / matrix-runtime",
    path: "matrix-runtime",
    path_with_namespace: "fe/matrix-runtime",
    created_at: "2021-03-08T12:16:43.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/matrix-runtime.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/matrix-runtime.git",
    web_url: "https://gitlab.qima-inc.com/fe/matrix-runtime",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-04T16:10:28.781Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11617,
    description: "\u4EA7\u54C1\u77E9\u9635\u7BA1\u7406\u540E\u53F0",
    name: "matrix-manage",
    name_with_namespace: "fe / matrix-manage",
    path: "matrix-manage",
    path_with_namespace: "fe/matrix-manage",
    created_at: "2021-03-08T03:22:55.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/matrix-manage.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/matrix-manage.git",
    web_url: "https://gitlab.qima-inc.com/fe/matrix-manage",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-04T16:10:28.346Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11302,
    description: "CDP(custom data platform) front end project",
    name: "cdp-front",
    name_with_namespace: "fe / cdp-front",
    path: "cdp-front",
    path_with_namespace: "fe/cdp-front",
    created_at: "2021-01-12T01:51:53.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/cdp-front.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/cdp-front.git",
    web_url: "https://gitlab.qima-inc.com/fe/cdp-front",
    readme_url: "https://gitlab.qima-inc.com/fe/cdp-front/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-07-04T16:10:20.140Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14137,
    description: "",
    name: "fe-service-mono",
    name_with_namespace: "fe / fe-service-mono",
    path: "fe-service-mono",
    path_with_namespace: "fe/fe-service-mono",
    created_at: "2022-04-25T02:27:58.289Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/fe-service-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/fe-service-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/fe-service-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/fe-service-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T16:10:18.720Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11952,
    description: "\u4F01\u5FAE\u52A9\u624B\u4EA4\u4ED8\u81EA\u52A8\u5316\u5E94\u7528\u90E8\u7F72\u4ED3\u5E93",
    name: "weass-automation-dist",
    name_with_namespace: "fe / weass-automation-dist",
    path: "weass-automation-dist",
    path_with_namespace: "fe/weass-automation-dist",
    created_at: "2021-04-22T06:33:23.147Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-automation-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-automation-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-automation-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T16:10:18.226Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 13058,
    description: "\u4F01\u5FAE\u52A9\u624B\u670D\u52A1\u5546\u81EA\u52A8\u5316 Node \u5E94\u7528",
    name: "weass-service-provider-automation-dist",
    name_with_namespace: "fe / weass-service-provider-automation-dist",
    path: "weass-service-provider-automation-dist",
    path_with_namespace: "fe/weass-service-provider-automation-dist",
    created_at: "2021-10-26T07:54:26.228Z",
    default_branch: "feat/service-automation-configuration",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-service-provider-automation-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-service-provider-automation-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T16:10:17.763Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 7703,
    description: "",
    name: "wsc-fe-pc-decorate-components",
    name_with_namespace: "wsc-node / wsc-fe-pc-decorate-components",
    path: "wsc-fe-pc-decorate-components",
    path_with_namespace: "wsc-node/wsc-fe-pc-decorate-components",
    created_at: "2019-07-09T02:26:01.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-fe-pc-decorate-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-pc-decorate-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-04T06:57:19.281Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 14843,
    description: "",
    name: "cloud-api-types",
    name_with_namespace: "fe-middle-platform / cloud-api-types",
    path: "cloud-api-types",
    path_with_namespace: "fe-middle-platform/cloud-api-types",
    created_at: "2023-11-07T02:45:26.728Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/cloud-api-types.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/cloud-api-types",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T06:24:10.877Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 11966,
    description: "Awesome React Hooks ",
    name: "react-hooks",
    name_with_namespace: "fe / react-hooks",
    path: "react-hooks",
    path_with_namespace: "fe/react-hooks",
    created_at: "2021-04-23T08:32:22.015Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/react-hooks.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/react-hooks.git",
    web_url: "https://gitlab.qima-inc.com/fe/react-hooks",
    readme_url: "https://gitlab.qima-inc.com/fe/react-hooks/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-04T03:27:01.385Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11887,
    description: "",
    name: "ump-biz-utils",
    name_with_namespace: "fe / ump-biz-utils",
    path: "ump-biz-utils",
    path_with_namespace: "fe/ump-biz-utils",
    created_at: "2021-04-16T08:34:24.845Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ump-biz-utils.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ump-biz-utils.git",
    web_url: "https://gitlab.qima-inc.com/fe/ump-biz-utils",
    readme_url: "https://gitlab.qima-inc.com/fe/ump-biz-utils/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-04T02:48:27.151Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12826,
    description: "\u89C6\u9891\u53F7H5\u4E1A\u52A1",
    name: "wsc-h5-wxvideo",
    name_with_namespace: "wsc-node / wsc-h5-wxvideo",
    path: "wsc-h5-wxvideo",
    path_with_namespace: "wsc-node/wsc-h5-wxvideo",
    created_at: "2021-09-15T06:43:13.254Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-wxvideo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-wxvideo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-04T02:40:36.739Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 2404,
    description: "\u5206\u9500\u524D\u7AEF\u4ED3\u5E93\uFF0C\u4ECE\u539F\u6765\u7684iron-front clone\u51FA\u6765\u7684\u3002",
    name: "fenxiao",
    name_with_namespace: "fe / fenxiao",
    path: "fenxiao",
    path_with_namespace: "fe/fenxiao",
    created_at: "2017-04-26T09:54:50.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/fenxiao.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/fenxiao.git",
    web_url: "https://gitlab.qima-inc.com/fe/fenxiao",
    readme_url: "https://gitlab.qima-inc.com/fe/fenxiao/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-03T12:03:03.052Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11867,
    description: "",
    name: "goods-biz-utils",
    name_with_namespace: "fe / goods-biz-utils",
    path: "goods-biz-utils",
    path_with_namespace: "fe/goods-biz-utils",
    created_at: "2021-04-14T10:25:48.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/goods-biz-utils.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/goods-biz-utils.git",
    web_url: "https://gitlab.qima-inc.com/fe/goods-biz-utils",
    readme_url: "https://gitlab.qima-inc.com/fe/goods-biz-utils/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-03T08:29:11.894Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12055,
    description: "",
    name: "Npm Platform Web Dist",
    name_with_namespace: "fe / Npm Platform Web Dist",
    path: "*********************",
    path_with_namespace: "fe/*********************",
    created_at: "2021-05-10T12:31:03.365Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/*********************.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/*********************.git",
    web_url: "https://gitlab.qima-inc.com/fe/*********************",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-03T08:15:07.059Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10213,
    description: "",
    name: "wsc-tee-goods",
    name_with_namespace: "weapp / wsc-tee-goods",
    path: "wsc-tee-goods",
    path_with_namespace: "weapp/wsc-tee-goods",
    created_at: "2020-08-04T09:08:23.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-goods.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-goods.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-goods",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-goods/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-03T07:48:35.688Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11084,
    description: "",
    name: "wsc-tee-shop",
    name_with_namespace: "weapp / wsc-tee-shop",
    path: "wsc-tee-shop",
    path_with_namespace: "weapp/wsc-tee-shop",
    created_at: "2020-12-14T02:52:46.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-shop.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-shop.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-shop",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-shop/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-03T06:55:48.705Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 15044,
    description: "",
    name: "ipaas",
    name_with_namespace: "fe / ipaas",
    path: "ipaas",
    path_with_namespace: "fe/ipaas",
    created_at: "2024-08-23T09:58:54.262Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ipaas.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ipaas.git",
    web_url: "https://gitlab.qima-inc.com/fe/ipaas",
    readme_url: "https://gitlab.qima-inc.com/fe/ipaas/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-03T06:05:20.720Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10177,
    description: "Tee\u7684\u4E1A\u52A1\u751F\u6001",
    name: "tee-biz",
    name_with_namespace: "fe-middle-platform / tee-biz",
    path: "tee-biz",
    path_with_namespace: "fe-middle-platform/tee-biz",
    created_at: "2020-07-29T08:55:04.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/tee-biz.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/tee-biz/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-03T03:34:15.702Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 12835,
    description: "",
    name: "ungoro-mono",
    name_with_namespace: "fe / ungoro-mono",
    path: "ungoro-mono",
    path_with_namespace: "fe/ungoro-mono",
    created_at: "2021-09-15T10:41:55.415Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ungoro-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ungoro-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/ungoro-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/ungoro-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-03T02:53:32.101Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5377,
    description: "\u9762\u5411\u5546\u5BB6\u7AEF \u2014\u2014 \u5E10\u53F7\u7CFB\u7EDF",
    name: "wsc-pc-account",
    name_with_namespace: "wsc-node / wsc-pc-account",
    path: "wsc-pc-account",
    path_with_namespace: "wsc-node/wsc-pc-account",
    created_at: "2018-08-10T03:08:15.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-account.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-account/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 1,
    star_count: 1,
    last_activity_at: "2025-07-02T16:00:07.466Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 10070,
    description: "\u5E10\u53F7\u4FA7\u7EDF\u4E00 NPM \u5305\u96C6\u5408",
    name: "account-plugin",
    name_with_namespace: "fe / account-plugin",
    path: "account-plugin",
    path_with_namespace: "fe/account-plugin",
    created_at: "2020-07-15T08:52:52.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/account-plugin.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/account-plugin.git",
    web_url: "https://gitlab.qima-inc.com/fe/account-plugin",
    readme_url: "https://gitlab.qima-inc.com/fe/account-plugin/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/10070/account-128.jpg",
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-02T10:10:42.172Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5939,
    description: "\u9762\u5411\u6D88\u8D39\u8005\u7AEF \u2014\u2014 \u5E10\u53F7\u7CFB\u7EDF",
    name: "wsc-h5-account",
    name_with_namespace: "wsc-node / wsc-h5-account",
    path: "wsc-h5-account",
    path_with_namespace: "wsc-node/wsc-h5-account",
    created_at: "2018-11-01T08:32:03.000Z",
    default_branch: "master",
    tag_list: ["H5", "\u8D26\u53F7"],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-account.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-account/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/5939/account-128.jpg",
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-02T08:14:59.011Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 15310,
    description: "\u5C0F\u7EA2\u4E66Demo\u5C55\u793A\u5C0F\u7A0B\u5E8F",
    name: "demoApp",
    name_with_namespace: "fe / demoApp",
    path: "xhsdemo",
    path_with_namespace: "fe/xhsdemo",
    created_at: "2025-05-07T06:11:14.776Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/xhsdemo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/xhsdemo.git",
    web_url: "https://gitlab.qima-inc.com/fe/xhsdemo",
    readme_url: "https://gitlab.qima-inc.com/fe/xhsdemo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-07-02T07:32:41.544Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 8177,
    description: "\u884C\u4E1A\u7535\u5546\uFF1A\u9152\u5E97",
    name: "wsc-pc-industry",
    name_with_namespace: "wsc-node / wsc-pc-industry",
    path: "wsc-pc-industry",
    path_with_namespace: "wsc-node/wsc-pc-industry",
    created_at: "2019-09-11T08:02:59.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-industry.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-industry/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-02T03:53:34.294Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 12509,
    description: "",
    name: "ext-tee-guide",
    name_with_namespace: "weapp / ext-tee-guide",
    path: "ext-tee-guide",
    path_with_namespace: "weapp/ext-tee-guide",
    created_at: "2021-07-22T03:45:14.504Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-guide.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-guide.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-guide",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-guide/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-01T12:17:53.532Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 15325,
    description: "",
    name: "open-webui",
    name_with_namespace: "fe / open-webui",
    path: "open-webui",
    path_with_namespace: "fe/open-webui",
    created_at: "2025-05-20T10:10:42.115Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/open-webui.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/open-webui.git",
    web_url: "https://gitlab.qima-inc.com/fe/open-webui",
    readme_url: "https://gitlab.qima-inc.com/fe/open-webui/-/blob/main/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-01T08:50:23.498Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15257,
    description: "",
    name: "rpa-proxy",
    name_with_namespace: "fe-middle-platform / rpa-proxy",
    path: "rpa-proxy",
    path_with_namespace: "fe-middle-platform/rpa-proxy",
    created_at: "2025-03-25T12:01:00.285Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/rpa-proxy.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/rpa-proxy/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-01T06:15:28.791Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 11988,
    description: "\u6D88\u606F - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-wsc-im",
    name_with_namespace: "weapp / ext-tee-wsc-im",
    path: "ext-tee-wsc-im",
    path_with_namespace: "weapp/ext-tee-wsc-im",
    created_at: "2021-04-26T09:55:17.588Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-wsc-im.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-im/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-07-01T03:52:03.907Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 10215,
    description: "\u65E9\u671F\u7EAF\u591A\u7AEF\u4ED3\u5E93(\u88ABwsc-tee-base\u4F9D\u8D56)\uFF0C\u88C5\u4FEE\u5B50\u4ED3\u5E93\uFF0C\u76EE\u524D\u4EC5\u7528\u4E8EQQ\u3001\u652F\u4ED8\u5B9D\u5C0F\u7A0B\u5E8F",
    name: "wsc-tee-decorate",
    name_with_namespace: "weapp / wsc-tee-decorate",
    path: "wsc-tee-decorate",
    path_with_namespace: "weapp/wsc-tee-decorate",
    created_at: "2020-08-04T09:09:09.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-decorate.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-decorate/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-07-01T03:14:02.554Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 9950,
    description: "\u591A\u7AEF Vant \u7EC4\u4EF6\u5E93",
    name: "vant-tee",
    name_with_namespace: "fe-middle-platform / vant-tee",
    path: "vant-tee",
    path_with_namespace: "fe-middle-platform/vant-tee",
    created_at: "2020-07-02T03:17:14.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/vant-tee.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/vant-tee/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-06-30T12:02:46.370Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 81,
    description: "\u6709\u8D5E\u5B98\u7F51",
    name: "intro",
    name_with_namespace: "fe / intro",
    path: "intro",
    path_with_namespace: "fe/intro",
    created_at: "2016-01-06T06:28:22.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/intro.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/intro.git",
    web_url: "https://gitlab.qima-inc.com/fe/intro",
    readme_url: "https://gitlab.qima-inc.com/fe/intro/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 8,
    last_activity_at: "2025-06-30T09:13:41.153Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14911,
    description: "",
    name: "minishopify-sdk",
    name_with_namespace: "weapp / minishopify-sdk",
    path: "minishopify-sdk",
    path_with_namespace: "weapp/minishopify-sdk",
    created_at: "2024-02-29T08:28:41.205Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/minishopify-sdk.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/minishopify-sdk.git",
    web_url: "https://gitlab.qima-inc.com/weapp/minishopify-sdk",
    readme_url: "https://gitlab.qima-inc.com/weapp/minishopify-sdk/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-30T08:28:20.934Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 12780,
    description: "\u8FDE\u9501B\u7AEFPC\u90E8\u7F72\u4ED3\u5E93",
    name: "chain-b-pc-dist",
    name_with_namespace: "wsc-node / chain-b-pc-dist",
    path: "chain-b-pc-dist",
    path_with_namespace: "wsc-node/chain-b-pc-dist",
    created_at: "2021-09-07T07:19:06.127Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/chain-b-pc-dist.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/chain-b-pc-dist",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-30T07:52:44.592Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6806,
    description: "\u8D44\u4EA7\u91D1\u878D\u53D1\u5E03\u9879\u76EE\uFF08\u4E0D\u9700\u8981\u7EF4\u62A4\u3001\u53EA\u7528\u6237\u53D1\u5E03\uFF09",
    name: "assets-fin",
    name_with_namespace: "fe / assets-fin",
    path: "assets-fin",
    path_with_namespace: "fe/assets-fin",
    created_at: "2019-03-04T02:54:21.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/assets-fin.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/assets-fin.git",
    web_url: "https://gitlab.qima-inc.com/fe/assets-fin",
    readme_url: "https://gitlab.qima-inc.com/fe/assets-fin/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-30T07:40:22.308Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5769,
    description: "\u9AD8\u6C47\u901A\u5E73\u53F0 \u5783\u573E\u6876",
    name: "gaohuitong-merchant",
    name_with_namespace: "fe / gaohuitong-merchant",
    path: "gaohuitong-merchant",
    path_with_namespace: "fe/gaohuitong-merchant",
    created_at: "2018-09-29T08:10:38.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/gaohuitong-merchant.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/gaohuitong-merchant.git",
    web_url: "https://gitlab.qima-inc.com/fe/gaohuitong-merchant",
    readme_url: "https://gitlab.qima-inc.com/fe/gaohuitong-merchant/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-30T07:39:18.480Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 9024,
    description: "\u524D\u7AEF\u5DE5\u7A0B\u5316\u4F53\u7CFB koko",
    name: "koko",
    name_with_namespace: "fe / koko",
    path: "koko",
    path_with_namespace: "fe/koko",
    created_at: "2020-02-17T03:06:12.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/koko.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/koko.git",
    web_url: "https://gitlab.qima-inc.com/fe/koko",
    readme_url: "https://gitlab.qima-inc.com/fe/koko/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 1,
    star_count: 13,
    last_activity_at: "2025-06-30T06:48:41.460Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14711,
    description: "",
    name: "youzan-utils",
    name_with_namespace: "fe / youzan-utils",
    path: "youzan-utils",
    path_with_namespace: "fe/youzan-utils",
    created_at: "2023-06-25T11:23:33.347Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/youzan-utils.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/youzan-utils.git",
    web_url: "https://gitlab.qima-inc.com/fe/youzan-utils",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-30T03:54:17.789Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14942,
    description: "",
    name: "diff-pack-core",
    name_with_namespace: "weapp / diff-pack-core",
    path: "diff-pack-core",
    path_with_namespace: "weapp/diff-pack-core",
    created_at: "2024-04-03T03:56:23.494Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/diff-pack-core.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/diff-pack-core.git",
    web_url: "https://gitlab.qima-inc.com/weapp/diff-pack-core",
    readme_url: "https://gitlab.qima-inc.com/weapp/diff-pack-core/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-27T09:00:33.612Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 2355,
    description: "",
    name: "staticRedirectRules",
    name_with_namespace: "fe / staticRedirectRules",
    path: "staticRedirectRules",
    path_with_namespace: "fe/staticRedirectRules",
    created_at: "2017-04-19T05:49:51.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/staticRedirectRules.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/staticRedirectRules.git",
    web_url: "https://gitlab.qima-inc.com/fe/staticRedirectRules",
    readme_url: "https://gitlab.qima-inc.com/fe/staticRedirectRules/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 3,
    star_count: 35,
    last_activity_at: "2025-06-27T03:11:32.011Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 8976,
    description: "\u7535\u5546\u524D\u7AEF\u5185\u90E8\u5DE5\u5177\u5E73\u53F0",
    name: "fe-platform",
    name_with_namespace: "ebiz-web / fe-platform",
    path: "fe-platform",
    path_with_namespace: "ebiz-web/fe-platform",
    created_at: "2020-02-04T13:14:55.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:ebiz-web/fe-platform.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/ebiz-web/fe-platform.git",
    web_url: "https://gitlab.qima-inc.com/ebiz-web/fe-platform",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-26T08:19:16.997Z",
    namespace: {
      id: 723,
      name: "ebiz-web",
      path: "ebiz-web",
      kind: "group",
      full_path: "ebiz-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/723/2_a499d8d8c31983ce1b5d1e45e629e061_con.png",
      web_url: "https://gitlab.qima-inc.com/groups/ebiz-web"
    }
  },
  {
    id: 15412,
    description: "",
    name: "ai-do",
    name_with_namespace: "fe / ai-do",
    path: "ai-do",
    path_with_namespace: "fe/ai-do",
    created_at: "2025-06-17T13:31:52.969Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ai-do.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ai-do.git",
    web_url: "https://gitlab.qima-inc.com/fe/ai-do",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-26T04:09:32.859Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11371,
    description: "",
    name: "wsc-h5-shopcore",
    name_with_namespace: "wsc-node / wsc-h5-shopcore",
    path: "wsc-h5-shopcore",
    path_with_namespace: "wsc-node/wsc-h5-shopcore",
    created_at: "2021-01-22T09:10:02.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-shopcore.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-shopcore/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-26T01:52:28.994Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 15456,
    description: "",
    name: "rpa-service",
    name_with_namespace: "fe-middle-platform / rpa-service",
    path: "rpa-service",
    path_with_namespace: "fe-middle-platform/rpa-service",
    created_at: "2025-06-25T07:38:32.336Z",
    default_branch: null,
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/rpa-service.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/rpa-service.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/rpa-service",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-25T07:38:32.336Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 7490,
    description: "\u8D44\u4EA7h5\u7AEF\u4E1A\u52A1\u7EC4\u4EF6\u5E93\uFF0C\u72EC\u7ACB\u53D1\u5B50\u5305",
    name: "assets-h5-components",
    name_with_namespace: "fe-assets / assets-h5-components",
    path: "assets-h5-components",
    path_with_namespace: "fe-assets/assets-h5-components",
    created_at: "2019-06-04T08:00:57.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-assets/assets-h5-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-assets/assets-h5-components.git",
    web_url: "https://gitlab.qima-inc.com/fe-assets/assets-h5-components",
    readme_url: "https://gitlab.qima-inc.com/fe-assets/assets-h5-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-25T07:05:38.772Z",
    namespace: {
      id: 1442,
      name: "fe-assets",
      path: "fe-assets",
      kind: "group",
      full_path: "fe-assets",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/fe-assets"
    }
  },
  {
    id: 11624,
    description: "",
    name: "matrix-mono",
    name_with_namespace: "fe / matrix-mono",
    path: "matrix-mono",
    path_with_namespace: "fe/matrix-mono",
    created_at: "2021-03-08T12:16:05.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/matrix-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/matrix-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/matrix-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/matrix-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-25T03:37:55.685Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6410,
    description: "",
    name: "zan-proxy-plugins",
    name_with_namespace: "fe / zan-proxy-plugins",
    path: "zan-proxy-plugins",
    path_with_namespace: "fe/zan-proxy-plugins",
    created_at: "2019-01-02T07:01:35.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/zan-proxy-plugins.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/zan-proxy-plugins.git",
    web_url: "https://gitlab.qima-inc.com/fe/zan-proxy-plugins",
    readme_url: "https://gitlab.qima-inc.com/fe/zan-proxy-plugins/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-24T12:51:16.006Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11915,
    description: "\u5E97\u94FA - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-shop",
    name_with_namespace: "weapp / ext-tee-shop",
    path: "ext-tee-shop",
    path_with_namespace: "weapp/ext-tee-shop",
    created_at: "2021-04-19T07:24:26.306Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-shop.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-shop.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-shop",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-shop/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-24T09:55:02.389Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11645,
    description: "",
    name: "wsc-tee-trade-common",
    name_with_namespace: "weapp / wsc-tee-trade-common",
    path: "wsc-tee-trade-common",
    path_with_namespace: "weapp/wsc-tee-trade-common",
    created_at: "2021-03-11T02:14:43.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-tee-trade-common.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-tee-trade-common/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 3,
    last_activity_at: "2025-06-23T08:32:52.893Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 14374,
    description: "ranta-configurator v2",
    name: "ranta-conf",
    name_with_namespace: "fe-middle-platform / ranta-conf",
    path: "ranta-conf",
    path_with_namespace: "fe-middle-platform/ranta-conf",
    created_at: "2022-10-17T10:41:40.123Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-conf.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-conf/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-19T13:01:08.206Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 15299,
    description: "\u7528\u4E8E\u627F\u8F7DAI\u76F8\u5173\u7684\u529F\u80FD\u57FA\u5EFA\u3002",
    name: "Ai Infrastructure",
    name_with_namespace: "wsc-node / Ai Infrastructure",
    path: "ai-infrastructure",
    path_with_namespace: "wsc-node/ai-infrastructure",
    created_at: "2025-04-17T01:59:59.410Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/ai-infrastructure.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/ai-infrastructure/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-19T08:32:09.245Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6693,
    description: "B\u7AEF\u5546\u4E1A\u5316\u8BA2\u8D2D\u4E1A\u52A1 ",
    name: "wsc-pc-subscribe",
    name_with_namespace: "enable-platform-frontend / wsc-pc-subscribe",
    path: "wsc-pc-subscribe",
    path_with_namespace: "enable-platform-frontend/wsc-pc-subscribe",
    created_at: "2019-02-12T07:00:00.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:enable-platform-frontend/wsc-pc-subscribe.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe.git",
    web_url: "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe",
    readme_url: "https://gitlab.qima-inc.com/enable-platform-frontend/wsc-pc-subscribe/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-06-19T03:27:41.776Z",
    namespace: {
      id: 1147,
      name: "enable-platform-frontend",
      path: "enable-platform-frontend",
      kind: "group",
      full_path: "enable-platform-frontend",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/enable-platform-frontend"
    }
  },
  {
    id: 4574,
    description: "\u6709\u8D5E\u8D22\u52A1\u7CFB\u7EDF",
    name: "finance",
    name_with_namespace: "fe / finance",
    path: "finance",
    path_with_namespace: "fe/finance",
    created_at: "2018-04-17T03:22:01.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/finance.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/finance.git",
    web_url: "https://gitlab.qima-inc.com/fe/finance",
    readme_url: "https://gitlab.qima-inc.com/fe/finance/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-19T03:27:39.254Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5692,
    description: "",
    name: "assets-finance",
    name_with_namespace: "fe / assets-finance",
    path: "assets-finance",
    path_with_namespace: "fe/assets-finance",
    created_at: "2018-09-19T07:00:07.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/assets-finance.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/assets-finance.git",
    web_url: "https://gitlab.qima-inc.com/fe/assets-finance",
    readme_url: "https://gitlab.qima-inc.com/fe/assets-finance/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-19T03:27:36.519Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 259,
    description: "",
    name: "tech",
    name_with_namespace: "fe / tech",
    path: "tech",
    path_with_namespace: "fe/tech",
    created_at: "2016-03-22T03:40:04.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/tech.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/tech.git",
    web_url: "https://gitlab.qima-inc.com/fe/tech",
    readme_url: "https://gitlab.qima-inc.com/fe/tech/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-19T03:27:35.337Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 8437,
    description: "",
    name: "h5-plugins",
    name_with_namespace: "wsc-node / h5-plugins",
    path: "h5-plugins",
    path_with_namespace: "wsc-node/h5-plugins",
    created_at: "2019-10-29T03:07:33.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/h5-plugins.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/h5-plugins.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/h5-plugins",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/h5-plugins/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-06-19T02:52:10.501Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 11945,
    description: "\u4F01\u5FAE\u52A9\u624B\u4EA4\u4ED8\u81EA\u52A8\u5316\u76F8\u5173\u6E90\u7801",
    name: "Weass Automation Mono",
    name_with_namespace: "fe / Weass Automation Mono",
    path: "weass-automation-mono",
    path_with_namespace: "fe/weass-automation-mono",
    created_at: "2021-04-21T06:26:07.441Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weass-automation-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weass-automation-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/weass-automation-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/weass-automation-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-18T06:39:22.738Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14839,
    description: "\u5730\u56FE\u670D\u52A1sdk",
    name: "yzmap-sdk",
    name_with_namespace: "xujiazheng / yzmap-sdk",
    path: "yzmap-sdk",
    path_with_namespace: "xujiazheng/yzmap-sdk",
    created_at: "2023-10-30T12:52:46.852Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:xujiazheng/yzmap-sdk.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk.git",
    web_url: "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk",
    readme_url: "https://gitlab.qima-inc.com/xujiazheng/yzmap-sdk/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-18T06:26:05.912Z",
    namespace: {
      id: 3020,
      name: "xujiazheng",
      path: "xujiazheng",
      kind: "user",
      full_path: "xujiazheng",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/xujiazheng"
    }
  },
  {
    id: 12146,
    description: "",
    name: "ranta-config-node-plugin",
    name_with_namespace: "fe-middle-platform / ranta-config-node-plugin",
    path: "ranta-config-node-plugin",
    path_with_namespace: "fe-middle-platform/ranta-config-node-plugin",
    created_at: "2021-05-25T09:36:36.091Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-config-node-plugin.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-config-node-plugin/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-18T06:24:56.596Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 12653,
    description: "\u4E2D\u53F0\u5316\u57FA\u7840\u5E93",
    name: "ranta-base-library",
    name_with_namespace: "fe-middle-platform / ranta-base-library",
    path: "ranta-base-library",
    path_with_namespace: "fe-middle-platform/ranta-base-library",
    created_at: "2021-08-16T12:15:56.440Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-middle-platform/ranta-base-library.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library.git",
    web_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library",
    readme_url: "https://gitlab.qima-inc.com/fe-middle-platform/ranta-base-library/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-16T09:11:31.092Z",
    namespace: {
      id: 2081,
      name: "fe-middle-platform",
      path: "fe-middle-platform",
      kind: "group",
      full_path: "fe-middle-platform",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/2081/images.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe-middle-platform"
    }
  },
  {
    id: 2507,
    description: "",
    name: "apidoc",
    name_with_namespace: "fe / apidoc",
    path: "apidoc",
    path_with_namespace: "fe/apidoc",
    created_at: "2017-05-12T07:41:19.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/apidoc.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/apidoc.git",
    web_url: "https://gitlab.qima-inc.com/fe/apidoc",
    readme_url: "https://gitlab.qima-inc.com/fe/apidoc/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-16T02:23:21.050Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6402,
    description: "",
    name: "zan-proxy-mac-client",
    name_with_namespace: "fe / zan-proxy-mac-client",
    path: "zan-proxy-mac-client",
    path_with_namespace: "fe/zan-proxy-mac-client",
    created_at: "2018-12-30T09:28:37.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/zan-proxy-mac-client.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client.git",
    web_url: "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client",
    readme_url: "https://gitlab.qima-inc.com/fe/zan-proxy-mac-client/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-06-16T02:10:17.969Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 11026,
    description: "",
    name: "data-base-demo",
    name_with_namespace: "fe / data-base-demo",
    path: "data-base-demo",
    path_with_namespace: "fe/data-base-demo",
    created_at: "2020-12-04T02:39:31.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/data-base-demo.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/data-base-demo.git",
    web_url: "https://gitlab.qima-inc.com/fe/data-base-demo",
    readme_url: "https://gitlab.qima-inc.com/fe/data-base-demo/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-14T10:45:24.104Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 14808,
    description: "",
    name: "Tee User Components",
    name_with_namespace: "fe / Tee User Components",
    path: "tee-user-components",
    path_with_namespace: "fe/tee-user-components",
    created_at: "2023-09-18T02:20:24.286Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/tee-user-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/tee-user-components.git",
    web_url: "https://gitlab.qima-inc.com/fe/tee-user-components",
    readme_url: "https://gitlab.qima-inc.com/fe/tee-user-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-13T08:54:39.735Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15284,
    description: "",
    name: "garden-zanuse",
    name_with_namespace: "fe / garden-zanuse",
    path: "garden-zanuse",
    path_with_namespace: "fe/garden-zanuse",
    created_at: "2025-04-09T02:39:02.597Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-zanuse.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-zanuse.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-zanuse",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-zanuse/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-11T09:55:10.371Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 10821,
    description: "Inner application manage platform\uFF0C\u5185\u90E8\u5E94\u7528\u7BA1\u7406\u5E73\u53F0",
    name: "iamp",
    name_with_namespace: "iamp / iamp",
    path: "iamp",
    path_with_namespace: "iamp/iamp",
    created_at: "2020-11-03T06:51:16.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:iamp/iamp.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/iamp/iamp.git",
    web_url: "https://gitlab.qima-inc.com/iamp/iamp",
    readme_url: "https://gitlab.qima-inc.com/iamp/iamp/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-11T02:33:43.445Z",
    namespace: {
      id: 2306,
      name: "iamp",
      path: "iamp",
      kind: "group",
      full_path: "iamp",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/iamp"
    }
  },
  {
    id: 7609,
    description: "http://fedoc.qima-inc.com/",
    name: "fe-doc",
    name_with_namespace: "fe / fe-doc",
    path: "fe-doc",
    path_with_namespace: "fe/fe-doc",
    created_at: "2019-06-25T02:18:02.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/fe-doc.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/fe-doc.git",
    web_url: "https://gitlab.qima-inc.com/fe/fe-doc",
    readme_url: "https://gitlab.qima-inc.com/fe/fe-doc/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-06-11T02:33:42.603Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6091,
    description: "\u6709\u8D5E\u8D22\u52A1\u5E73\u53F0 - \u5BF9\u5916\uFF08\u4EE3\u7406\u5546\u548C\u670D\u52A1\u5546\uFF09\r\ncommercial-finance.youzan.com",
    name: "finance-external",
    name_with_namespace: "fe / finance-external",
    path: "finance-external",
    path_with_namespace: "fe/finance-external",
    created_at: "2018-11-23T06:27:34.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/finance-external.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/finance-external.git",
    web_url: "https://gitlab.qima-inc.com/fe/finance-external",
    readme_url: "https://gitlab.qima-inc.com/fe/finance-external/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-11T02:33:42.031Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15021,
    description: "",
    name: "garden-ai-sales",
    name_with_namespace: "fe / garden-ai-sales",
    path: "garden-ai-sales",
    path_with_namespace: "fe/garden-ai-sales",
    created_at: "2024-07-31T02:45:29.639Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/garden-ai-sales.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/garden-ai-sales.git",
    web_url: "https://gitlab.qima-inc.com/fe/garden-ai-sales",
    readme_url: "https://gitlab.qima-inc.com/fe/garden-ai-sales/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-09T13:05:08.517Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 5654,
    description: "",
    name: "retail-shared",
    name_with_namespace: "retail-web / retail-shared",
    path: "retail-shared",
    path_with_namespace: "retail-web/retail-shared",
    created_at: "2018-09-13T02:01:25.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-shared.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-shared.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-shared",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-shared/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-09T08:47:56.856Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 7056,
    description: "",
    name: "youzan-security",
    name_with_namespace: "wsc-node / youzan-security",
    path: "youzan-security",
    path_with_namespace: "wsc-node/youzan-security",
    created_at: "2019-04-12T08:02:34.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/youzan-security.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/youzan-security.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/youzan-security",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/youzan-security/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-05T09:02:12.574Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6266,
    description: "\u6709\u8D5E\u7CBE\u8D5E\u62A5\u540D\u7CFB\u7EDF\u3001\u6709\u8D5E\u7CBE\u9009\u751F\u6D3B\u53F7",
    name: "wsc-pc-mars",
    name_with_namespace: "wsc-node / wsc-pc-mars",
    path: "wsc-pc-mars",
    path_with_namespace: "wsc-node/wsc-pc-mars",
    created_at: "2018-12-12T07:17:37.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-mars.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-mars/-/blob/master/README.md",
    avatar_url: "https://gitlab.qima-inc.com/uploads/-/system/project/avatar/6266/basicprofile.jpeg",
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-04T10:07:56.518Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 7476,
    description: "\u4E09\u65B9\u6E20\u9053\u63A5\u5165\u7EDF\u4E00\u7F51\u5173\uFF0C\u50CF\u5FAE\u4FE1\u3001\u5FAE\u535A\u3001\u5FEB\u624B\u3001\u767E\u5EA6\u3001\u864E\u7259\u3001\u964C\u964C\u3001\u77E5\u4E4E\u7B49\u5404\u4E09\u65B9\u5E73\u53F0",
    name: "channel-gateway",
    name_with_namespace: "wsc-node / channel-gateway",
    path: "channel-gateway",
    path_with_namespace: "wsc-node/channel-gateway",
    created_at: "2019-06-03T08:30:42.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/channel-gateway.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/channel-gateway.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/channel-gateway",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/channel-gateway/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-04T06:55:00.859Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 6038,
    description: "\u6709\u8D5E JSBridge \u9002\u914D\u5C42\uFF08Zan Native Bridge\uFF09",
    name: "ZNB",
    name_with_namespace: "fe / ZNB",
    path: "ZNB",
    path_with_namespace: "fe/ZNB",
    created_at: "2018-11-15T08:09:38.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/ZNB.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/ZNB.git",
    web_url: "https://gitlab.qima-inc.com/fe/ZNB",
    readme_url: "https://gitlab.qima-inc.com/fe/ZNB/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-06-04T02:05:44.401Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 12214,
    description: "\u4E0B\u5355\u9875\u50A8\u503C\u7EC4\u4EF6\u591A\u7AEF",
    name: "ext-tee-retail-prepaid",
    name_with_namespace: "weapp / ext-tee-retail-prepaid",
    path: "ext-tee-retail-prepaid",
    path_with_namespace: "weapp/ext-tee-retail-prepaid",
    created_at: "2021-06-07T03:16:53.920Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-retail-prepaid.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-retail-prepaid/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-06-03T08:14:33.478Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 4980,
    description: "",
    name: "weapp-log-sdk",
    name_with_namespace: "fe / weapp-log-sdk",
    path: "weapp-log-sdk",
    path_with_namespace: "fe/weapp-log-sdk",
    created_at: "2018-06-14T11:44:06.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/weapp-log-sdk.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/weapp-log-sdk.git",
    web_url: "https://gitlab.qima-inc.com/fe/weapp-log-sdk",
    readme_url: "https://gitlab.qima-inc.com/fe/weapp-log-sdk/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-03T07:20:59.974Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 15190,
    description: "AI\u76F8\u5173\u7684\u6742\u9879\u5185\u5BB9\uFF0C\u811A\u672C\u4EC0\u4E48\u7684",
    name: "ai_things",
    name_with_namespace: "mobile / ai_things",
    path: "ai_things",
    path_with_namespace: "mobile/ai_things",
    created_at: "2025-02-24T03:41:20.339Z",
    default_branch: "main",
    tag_list: [],
    ssh_url_to_repo: "***********************:mobile/ai_things.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/mobile/ai_things.git",
    web_url: "https://gitlab.qima-inc.com/mobile/ai_things",
    readme_url: "https://gitlab.qima-inc.com/mobile/ai_things/-/blob/main/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-06-03T06:37:07.682Z",
    namespace: {
      id: 349,
      name: "mobile",
      path: "mobile",
      kind: "group",
      full_path: "mobile",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/mobile"
    }
  },
  {
    id: 5196,
    description: "wsc-h5\u516C\u7528\u4EE3\u7801",
    name: "wsc-fe-h5-shared",
    name_with_namespace: "wsc-node / wsc-fe-h5-shared",
    path: "wsc-fe-h5-shared",
    path_with_namespace: "wsc-node/wsc-fe-h5-shared",
    created_at: "2018-07-18T09:40:40.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-fe-h5-shared.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-fe-h5-shared/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 5,
    last_activity_at: "2025-05-29T02:20:23.811Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 5883,
    description: "\u9762\u5411\u6D88\u8D39\u8005\u7AEF \u2014\u2014 \u524D\u7AEF\u7EC4\u4EF6\u5316\u5BB9\u5668",
    name: "wsc-h5-components",
    name_with_namespace: "wsc-node / wsc-h5-components",
    path: "wsc-h5-components",
    path_with_namespace: "wsc-node/wsc-h5-components",
    created_at: "2018-10-25T03:09:56.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-h5-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components",
    readme_url: "https://gitlab.qima-inc.com/wsc-node/wsc-h5-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 9,
    last_activity_at: "2025-05-28T07:43:11.477Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  },
  {
    id: 13238,
    description: "\u5BFC\u8D2D\u524D\u7AEFmono\u4ED3\u5E93",
    name: "guide-mono",
    name_with_namespace: "fe / guide-mono",
    path: "guide-mono",
    path_with_namespace: "fe/guide-mono",
    created_at: "2021-11-17T07:09:49.603Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/guide-mono.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/guide-mono.git",
    web_url: "https://gitlab.qima-inc.com/fe/guide-mono",
    readme_url: "https://gitlab.qima-inc.com/fe/guide-mono/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-05-28T02:40:07.388Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 4626,
    description: "",
    name: "retail-node-base",
    name_with_namespace: "retail-web / retail-node-base",
    path: "retail-node-base",
    path_with_namespace: "retail-web/retail-node-base",
    created_at: "2018-04-24T10:11:51.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-node-base.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-node-base.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-node-base",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-node-base/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-05-27T11:34:39.033Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 3688,
    description: "\u96F6\u552E\u524D\u7AEF React \u4E1A\u52A1\u7EC4\u4EF6\u5E93",
    name: "retail-components",
    name_with_namespace: "retail-web / retail-components",
    path: "retail-components",
    path_with_namespace: "retail-web/retail-components",
    created_at: "2017-11-01T02:09:55.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:retail-web/retail-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/retail-web/retail-components.git",
    web_url: "https://gitlab.qima-inc.com/retail-web/retail-components",
    readme_url: "https://gitlab.qima-inc.com/retail-web/retail-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 8,
    last_activity_at: "2025-05-27T03:00:22.836Z",
    namespace: {
      id: 579,
      name: "retail-web",
      path: "retail-web",
      kind: "group",
      full_path: "retail-web",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/579/box-packing-peanut-512-min.png",
      web_url: "https://gitlab.qima-inc.com/groups/retail-web"
    }
  },
  {
    id: 11458,
    description: "\u6D77\u62A5\u6E32\u67D3\u670D\u52A1\u65B0\u4ED3\u5E93",
    name: "youzan-poster",
    name_with_namespace: "fe / youzan-poster",
    path: "youzan-poster",
    path_with_namespace: "fe/youzan-poster",
    created_at: "2021-02-04T03:04:10.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe/youzan-poster.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe/youzan-poster.git",
    web_url: "https://gitlab.qima-inc.com/fe/youzan-poster",
    readme_url: "https://gitlab.qima-inc.com/fe/youzan-poster/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-05-22T08:38:19.643Z",
    namespace: {
      id: 25,
      name: "fe",
      path: "fe",
      kind: "group",
      full_path: "fe",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/25/html-css-js.png",
      web_url: "https://gitlab.qima-inc.com/groups/fe"
    }
  },
  {
    id: 6646,
    description: "\u8D44\u4EA7\u4E1A\u52A1\u7EC4\u4EF6\u5E93",
    name: "assets-components",
    name_with_namespace: "fe-assets / assets-components",
    path: "assets-components",
    path_with_namespace: "fe-assets/assets-components",
    created_at: "2019-01-30T02:32:20.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:fe-assets/assets-components.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/fe-assets/assets-components.git",
    web_url: "https://gitlab.qima-inc.com/fe-assets/assets-components",
    readme_url: "https://gitlab.qima-inc.com/fe-assets/assets-components/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 0,
    last_activity_at: "2025-05-22T06:36:52.384Z",
    namespace: {
      id: 1442,
      name: "fe-assets",
      path: "fe-assets",
      kind: "group",
      full_path: "fe-assets",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/fe-assets"
    }
  },
  {
    id: 4260,
    description: "",
    name: "wsc-utils",
    name_with_namespace: "weapp / wsc-utils",
    path: "wsc-utils",
    path_with_namespace: "weapp/wsc-utils",
    created_at: "2018-02-20T08:37:01.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/wsc-utils.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/wsc-utils.git",
    web_url: "https://gitlab.qima-inc.com/weapp/wsc-utils",
    readme_url: "https://gitlab.qima-inc.com/weapp/wsc-utils/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 4,
    last_activity_at: "2025-05-21T03:51:49.799Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 11928,
    description: "\u8425\u9500 - \u591A\u7AEF\u4E2D\u53F0\u5316\u6269\u5C55\u4ED3\u5E93",
    name: "ext-tee-wsc-ump",
    name_with_namespace: "weapp / ext-tee-wsc-ump",
    path: "ext-tee-wsc-ump",
    path_with_namespace: "weapp/ext-tee-wsc-ump",
    created_at: "2021-04-20T06:40:33.483Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:weapp/ext-tee-wsc-ump.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump.git",
    web_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump",
    readme_url: "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-ump/-/blob/master/README.md",
    avatar_url: null,
    forks_count: 0,
    star_count: 1,
    last_activity_at: "2025-05-20T09:12:57.994Z",
    namespace: {
      id: 356,
      name: "weapp",
      path: "weapp",
      kind: "group",
      full_path: "weapp",
      parent_id: null,
      avatar_url: "/uploads/-/system/group/avatar/356/u_3144790471_4136229545_fm_21_gp_0.jpg",
      web_url: "https://gitlab.qima-inc.com/groups/weapp"
    }
  },
  {
    id: 8763,
    description: "\u589E\u957F\u4E2D\u5FC3\u5E7F\u544A\u3001CPS\u76F8\u5173\u4E1A\u52A1",
    name: "wsc-pc-cps",
    name_with_namespace: "wsc-node / wsc-pc-cps",
    path: "wsc-pc-cps",
    path_with_namespace: "wsc-node/wsc-pc-cps",
    created_at: "2019-12-20T02:59:51.000Z",
    default_branch: "master",
    tag_list: [],
    ssh_url_to_repo: "***********************:wsc-node/wsc-pc-cps.git",
    http_url_to_repo: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps.git",
    web_url: "https://gitlab.qima-inc.com/wsc-node/wsc-pc-cps",
    readme_url: null,
    avatar_url: null,
    forks_count: 0,
    star_count: 2,
    last_activity_at: "2025-05-16T16:13:02.977Z",
    namespace: {
      id: 1065,
      name: "wsc-node",
      path: "wsc-node",
      kind: "group",
      full_path: "wsc-node",
      parent_id: null,
      avatar_url: null,
      web_url: "https://gitlab.qima-inc.com/groups/wsc-node"
    }
  }
];

// src/gitlib/tools.ts
function getWscLastHotfixBranch() {
  return getBranchs({ appName: "wsc", search: "^hotfix/v2.2" }).then(
    (branches) => branches.sort(
      (a, b) => +new Date(b.commit.created_at) - +new Date(a.commit.created_at)
    )
  ).then((sortedBranches) => sortedBranches[0].name);
}
function getProjectRepo(appName) {
  return getProjectInfo({
    appName
  }).then((res) => res.ssh_url_to_repo);
}
function getProjectId(appName) {
  return git_projects_default.find((p) => p.name === appName)?.id;
}
function getFileContent({ appName, filePath, branch }) {
  return getRepositoryFileContent({
    appName,
    filePath,
    branch
  }).then((res) => Buffer.from(res.content, "base64").toString());
}
async function updateAppKokoRepo(options) {
  const { appName, branchName, subApps } = options;
  if (!appName || !branchName || !Array.isArray(subApps)) {
    throw new Error("appName, branchName\u548CsubApps\u90FD\u662F\u5FC5\u586B\u53C2\u6570");
  }
  return getFileContent({
    appName,
    filePath: "koko.repo.json",
    branch: branchName
  }).then((content) => {
    const kokoRepo = JSON.parse(content);
    kokoRepo.repos.forEach((repo) => {
      if (subApps.includes(repo.name)) {
        repo.branch = branchName;
      }
    });
    return updateRepositoryFileContent({
      appName,
      filePath: "koko.repo.json",
      branch: branchName,
      content: JSON.stringify(kokoRepo, null, 2),
      commit_message: `feat: \u66F4\u65B0${subApps}\u7684\u5206\u652F\u4E3A${branchName}`
    });
  });
}
async function updatePackageJson({
  appName,
  branchName,
  path: path2,
  packageInfo
}) {
  const filePath = path2 ? `${path2}/package.json` : "package.json";
  return getFileContent({
    appName,
    filePath,
    branch: branchName
  }).then((content) => {
    const packageJson = JSON.parse(content);
    const { name, version } = packageInfo;
    let packageFound = false;
    if (packageJson?.dependencies?.[name]) {
      packageJson.dependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.devDependencies?.[name]) {
      packageJson.devDependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.peerDependencies?.[name]) {
      packageJson.peerDependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.resolutions?.[name]) {
      packageJson.resolutions[name] = version;
      packageFound = true;
    }
    if (!packageFound) {
      throw new Error(`${appName}/${filePath}\u4E2D\u6CA1\u6709\u627E\u5230${name}\u7684\u4FE1\u606F\uFF0C\u8BF7\u68C0\u67E5\u76EE\u5F55\u662F\u5426\u6B63\u786E\u3002`);
    }
    return updateRepositoryFileContent({
      appName,
      filePath,
      branch: branchName,
      content: JSON.stringify(packageJson, null, 2),
      commit_message: `feat: \u66F4\u65B0${name}\u7684\u4F9D\u8D56\u7248\u672C\u4E3A${version}`
    });
  });
}

// src/packages/npm-publish/gitbash.ts
var appDir = process.env.app_dir || path.join(__dirname, "../github");
function getCurrentBranch() {
  try {
    return execSync("git branch --show-current", { encoding: "utf8" }).trim();
  } catch (error) {
    return "unknown";
  }
}
function isWorkingDirectoryClean() {
  try {
    const status = execSync("git status --porcelain", { encoding: "utf8" });
    return !status.trim();
  } catch (error) {
    return false;
  }
}
async function getOrCloneProjectDir(appName, baseGithubDir = appDir) {
  const projectDir = path.join(baseGithubDir, appName);
  if (fs.existsSync(projectDir)) {
    Logger.log(`\u2713 Project directory already exists: ${projectDir}`);
    return projectDir;
  }
  let gitUrl;
  try {
    gitUrl = await getProjectRepo(appName);
    if (!gitUrl) {
      throw new Error(`Failed to get repository URL for app '${appName}'`);
    }
  } catch (error) {
    throw new Error(`Failed to get git repository URL for app '${appName}': ${error.message}`);
  }
  Logger.log(`Project directory not found, cloning from: ${gitUrl}`);
  if (!fs.existsSync(baseGithubDir)) {
    fs.mkdirSync(baseGithubDir, { recursive: true });
    Logger.log(`Created base directory: ${baseGithubDir}`);
  }
  try {
    execSync(`git clone ${gitUrl} ${projectDir}`, { stdio: "inherit" });
    Logger.log(`\u2705 Successfully cloned project to: ${projectDir}`);
    return projectDir;
  } catch (error) {
    throw new Error(`Failed to clone project: ${error.message}`);
  }
}
async function createOrSwitchBranch(options) {
  const {
    appName,
    baseGithubDir = appDir,
    branchName,
    baseBranch = "master",
    cleanWorkingDir = true,
    updatePackages = false,
    packageName,
    packageVersion,
    commitAndPush = true,
    commitMessage,
    clientDir = ""
  } = options;
  if (!appName || !branchName) {
    throw new Error("appName and branchName are required");
  }
  const gitDir = await getOrCloneProjectDir(appName, baseGithubDir);
  const result = {
    success: false,
    currentBranch: null,
    targetBranch: branchName,
    operations: []
  };
  try {
    process.chdir(gitDir);
    Logger.log(`Changed directory to: ${gitDir}`);
    result.operations.push(`Changed to directory: ${gitDir}`);
    execSync("git rev-parse --is-inside-work-tree", { stdio: "ignore" });
    const currentBranch = getCurrentBranch();
    result.currentBranch = currentBranch;
    Logger.log(`Current branch: ${currentBranch}`);
    Logger.log(`Target branch: ${branchName}`);
    Logger.log(`\u{1F50D} Checking if remote branch '${branchName}' exists...`);
    let remoteBranchExists = false;
    try {
      await getBranch({ appName, branchName });
      remoteBranchExists = true;
      Logger.log(`\u2713 Remote branch '${branchName}' exists`);
      result.operations.push(`Remote branch exists: ${branchName}`);
    } catch (error) {
      Logger.log(`Remote branch '${branchName}' does not exist`);
      result.operations.push(`Remote branch does not exist: ${branchName}`);
    }
    if (!remoteBranchExists) {
      Logger.log(`\u{1F680} Creating remote branch '${branchName}' from '${baseBranch}'...`);
      try {
        await createBranch({
          appName,
          branchName,
          targetBranchName: baseBranch
        });
        Logger.log(`\u2705 Successfully created remote branch '${branchName}'`);
        result.operations.push(`Created remote branch: ${branchName} from ${baseBranch}`);
      } catch (error) {
        throw new Error(`Failed to create remote branch: ${error.message}`);
      }
    }
    if (updatePackages) {
      Logger.log("\n\u{1F504} Starting npm package update via API...");
      if (!packageName || !packageVersion) {
        throw new Error(
          "packageName and packageVersion are required when updatePackages is true"
        );
      }
      Logger.log(`\u{1F4E6} Updating package.json via API...`);
      try {
        await updatePackageJson({
          appName,
          branchName,
          path: clientDir || void 0,
          // 确保空字符串转换为undefined
          packageInfo: {
            name: packageName,
            version: packageVersion
          }
        });
        Logger.log(`\u2705 Successfully updated ${packageName} to ${packageVersion} via API`);
        result.operations.push(`Updated package via API: ${packageName}@${packageVersion}`);
      } catch (error) {
        throw new Error(`Failed to update package.json via API: ${error.message}`);
      }
    }
    Logger.log("\n\u{1F504} Starting local operations...");
    const workingPath = clientDir ? path.join(gitDir, clientDir) : gitDir;
    if (currentBranch === branchName) {
      Logger.log(`\u2713 Already on branch '${branchName}'`);
      if (isWorkingDirectoryClean()) {
        Logger.log("Working directory is clean, pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Pulled latest changes from origin/${branchName}`
        );
      } else if (cleanWorkingDir) {
        Logger.log("Working directory has changes, cleaning up first...");
        const status = execSync("git status --porcelain", { encoding: "utf8" });
        if (status.trim()) {
          Logger.log("Found uncommitted changes:");
          Logger.log(status);
          Logger.log("Cleaning up uncommitted changes...");
          execSync("git reset --hard HEAD", { stdio: "inherit" });
          Logger.log("\u2713 Reset all changes to last commit");
          result.operations.push("Reset uncommitted changes");
          execSync("git clean -fd", { stdio: "inherit" });
          Logger.log("\u2713 Removed untracked files and directories");
          result.operations.push("Removed untracked files");
        }
        Logger.log("Pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(
          `Pulled latest changes from origin/${branchName}`
        );
      } else {
        Logger.log(
          "Working directory has changes, skipping pull (cleanWorkingDir=false)"
        );
        result.operations.push("Skipped pull due to uncommitted changes");
      }
    } else {
      if (cleanWorkingDir) {
        Logger.log("Checking for uncommitted changes...");
        try {
          const status = execSync("git status --porcelain", {
            encoding: "utf8"
          });
          if (status.trim()) {
            Logger.log("Found uncommitted changes:");
            Logger.log(status);
            Logger.log("Cleaning up uncommitted changes...");
            execSync("git reset --hard HEAD", { stdio: "inherit" });
            Logger.log("\u2713 Reset all changes to last commit");
            result.operations.push("Reset uncommitted changes");
            execSync("git clean -fd", { stdio: "inherit" });
            Logger.log("\u2713 Removed untracked files and directories");
            result.operations.push("Removed untracked files");
            Logger.log("\u2713 Working directory is now clean");
          } else {
            Logger.log("\u2713 Working directory is already clean");
          }
        } catch (error) {
          Logger.log("Warning: Could not check git status, continuing...");
        }
      }
      Logger.log("\u{1F4E5} Fetching latest changes...");
      execSync("git fetch", { stdio: "inherit" });
      result.operations.push("Fetched latest changes");
      Logger.log(`\u{1F504} Switching to branch '${branchName}'...`);
      try {
        execSync(`git checkout ${branchName}`, { stdio: "inherit" });
        result.operations.push(`Checked out branch: ${branchName}`);
        Logger.log("Pulling latest changes...");
        execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
        result.operations.push(`Pulled latest changes from origin/${branchName}`);
      } catch (error) {
        Logger.log(`Local branch doesn't exist, creating from origin/${branchName}...`);
        execSync(`git checkout -b ${branchName} origin/${branchName}`, { stdio: "inherit" });
        result.operations.push(`Created local branch from origin/${branchName}`);
      }
    }
    Logger.log("\n\u{1F4E6} Installing dependencies...");
    if (!fs.existsSync(workingPath)) {
      throw new Error(`Working directory not found: ${workingPath}`);
    }
    Logger.log(`Entering working directory: ${workingPath}`);
    process.chdir(workingPath);
    result.operations.push(`Changed to working directory: ${workingPath}`);
    Logger.log(`\u{1F4E6} Running yarn install in: ${workingPath}`);
    execSync("yarn install --non-interactive", { stdio: "inherit" });
    result.operations.push(`Executed yarn install in: ${workingPath}`);
    Logger.log("\u2705 Dependencies installed successfully!");
    if (updatePackages) {
      Logger.log(`
\u{1F4E6} Re-running yarn install to update yarn.lock after API update...`);
      process.chdir(workingPath);
      execSync("yarn install --non-interactive", { stdio: "inherit" });
      result.operations.push(`Re-executed yarn install in: ${workingPath}`);
      Logger.log("\u2705 yarn.lock updated successfully!");
    }
    if (updatePackages && commitAndPush) {
      Logger.log("\n\u{1F4E4} Pushing local changes...");
      process.chdir(gitDir);
      Logger.log("\u{1F4E5} Fetching latest changes from remote...");
      execSync("git fetch", { stdio: "inherit" });
      execSync(`git pull origin ${branchName}`, { stdio: "inherit" });
      result.operations.push("Pulled latest changes including API updates");
      const yarnLockPath = clientDir ? `${clientDir}/yarn.lock` : "yarn.lock";
      try {
        execSync(`git add ${yarnLockPath}`, { stdio: "inherit" });
        Logger.log("Added yarn.lock to git");
        result.operations.push("Added yarn.lock to git");
        const status = execSync("git status --porcelain", {
          encoding: "utf8"
        });
        if (status.trim()) {
          const lockCommitMessage = commitMessage || `chore: update yarn.lock for ${packageName}@${packageVersion}`;
          execSync(`git commit -m "${lockCommitMessage}"`, {
            stdio: "inherit"
          });
          Logger.log(`Committed yarn.lock changes: ${lockCommitMessage}`);
          result.operations.push(`Committed: ${lockCommitMessage}`);
          execSync(`git push origin ${branchName}`, { stdio: "inherit" });
          Logger.log(`\u2705 Successfully pushed yarn.lock to origin/${branchName}`);
          result.operations.push(`Pushed yarn.lock to origin/${branchName}`);
        } else {
          Logger.log("No yarn.lock changes to commit");
          result.operations.push("No yarn.lock changes to commit");
        }
      } catch (error) {
        Logger.log("Warning: Could not add yarn.lock or no yarn.lock file found");
        result.operations.push("Warning: yarn.lock handling failed");
      }
    }
    result.success = true;
    return result;
  } catch (error) {
    result.error = error.message;
    throw error;
  }
}

// src/packages/npm-publish/task-custom.ts
async function taskWithCustomApps(publishConfig, customAppMaps, clientDir = "") {
  const { modules, branch_name } = publishConfig;
  if (branch_name === "master") {
    Logger.log("Skipping master branch");
    return { success: false, error: "Master branch not allowed" };
  }
  if (!modules || !Array.isArray(modules) || modules.length === 0) {
    Logger.log("No modules found in data");
    return { success: false, error: "No modules found" };
  }
  Logger.log(`Processing ${modules.length} modules...`);
  const results = [];
  try {
    for (const module of modules) {
      const { module_name, version } = module;
      Logger.log(`
\u{1F4E6} Processing module: ${module_name}@${version}`);
      if (!customAppMaps[module_name]) {
        Logger.log(
          `\u23ED\uFE0F  Module ${module_name} not found in custom app maps, skipping...`
        );
        continue;
      }
      const targetApps = customAppMaps[module_name];
      Logger.log(
        `\u{1F3AF} Found ${targetApps.length} target apps: ${targetApps.join(", ")}`
      );
      for (const appName of targetApps) {
        Logger.log(`
\u{1F680} Processing app: ${appName}`);
        const options = {
          appName,
          branchName: branch_name,
          updatePackages: true,
          packageName: module_name,
          packageVersion: version,
          clientDir,
          commitAndPush: true,
          commitMessage: `feat: update ${module_name} to ${version}`
        };
        try {
          const result = await createOrSwitchBranch(options);
          Logger.log(
            `\u2705 Successfully processed ${appName} for ${module_name}`
          );
          results.push({
            module_name,
            version,
            appName,
            success: true,
            operations: result.operations
          });
        } catch (error) {
          console.error(
            `\u274C Failed to process ${appName} for ${module_name}: ${error.message}`
          );
          results.push({
            module_name,
            version,
            appName,
            success: false,
            error: error.message
          });
        }
      }
    }
    const failedResults = results.filter((r) => !r.success);
    const successCount = results.filter((r) => r.success).length;
    Logger.log(
      `
\u{1F4CA} Summary: ${successCount}/${results.length} operations completed successfully`
    );
    if (failedResults.length > 0) {
      Logger.log("\u274C Failed operations:");
      failedResults.forEach((result) => {
        Logger.log(
          `   - ${result.appName} (${result.module_name}): ${result.error}`
        );
      });
    }
    return {
      success: failedResults.length === 0,
      totalModules: modules.length,
      processedOperations: results.length,
      successfulOperations: successCount,
      failedOperations: failedResults.length,
      results
    };
  } catch (error) {
    console.error(`\u274C Unexpected error during processing: ${error.message}`);
    return {
      success: false,
      error: error.message,
      results
    };
  }
}

// src/utils/webhook.ts
import https from "https";
import http from "http";
import { URL } from "url";
function fetch2(url, options = {}) {
  return new Promise((resolve3, reject) => {
    const urlObj = new URL(url);
    const { method = "GET", headers = {}, body } = options;
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === "https:" ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method.toUpperCase(),
      headers: {
        "User-Agent": "Node.js",
        ...headers
      }
    };
    if (body) {
      const bodyBuffer = Buffer.from(body, "utf8");
      requestOptions.headers["Content-Length"] = bodyBuffer.length;
    }
    const protocol = urlObj.protocol === "https:" ? https : http;
    const req = protocol.request(requestOptions, (res) => {
      let data = "";
      res.on("data", (chunk) => {
        data += chunk;
      });
      res.on("end", () => {
        const response = {
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          headers: res.headers,
          text: () => Promise.resolve(data),
          json: () => {
            try {
              return Promise.resolve(JSON.parse(data));
            } catch (e) {
              return Promise.reject(new Error("Invalid JSON response"));
            }
          }
        };
        resolve3(response);
      });
    });
    req.on("error", (error) => {
      reject(error);
    });
    if (body) {
      req.write(body);
    }
    req.end();
  });
}
var webhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/4306103e-2dfe-4427-9339-42b87d41aee3";
function notify(payload) {
  const url = process.env.webhook_url || webhookUrl;
  if (!url) {
    return Promise.resolve();
  }
  return fetch2(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(payload)
  });
}

// src/packages/npm-publish/notification.ts
import { execSync as execSync2 } from "child_process";
function getGitUserName() {
  try {
    return execSync2("git config user.name", { encoding: "utf8" }).trim();
  } catch (error) {
    console.warn("Failed to get git user name:", error.message);
    return "Unknown User";
  }
}
function generateNotificationText(result, originalData) {
  const { branch_name } = originalData;
  const user_name = getGitUserName();
  if (!result.success && (!result.results || result.results.length === 0)) {
    return `\u274C NPM\u5305\u66F4\u65B0\u5931\u8D25
\u7528\u6237: ${user_name}
\u5206\u652F: ${branch_name}
\u9519\u8BEF: ${result.error || "\u672A\u77E5\u9519\u8BEF"}`;
  }
  const { successfulOperations, failedOperations, results } = result;
  const successResults = results.filter((r) => r.success);
  const failedResults = results.filter((r) => !r.success);
  const title = result.success ? "\u2705 NPM\u5305\u66F4\u65B0\u5B8C\u6210" : "\u26A0\uFE0F NPM\u5305\u66F4\u65B0\u90E8\u5206\u5B8C\u6210";
  let text = `${title}
`;
  text += `\u7528\u6237: ${user_name}
`;
  text += `\u5206\u652F: ${branch_name}
`;
  text += `\u603B\u8BA1: ${successfulOperations}\u6210\u529F / ${failedOperations}\u5931\u8D25

`;
  if (successResults.length > 0) {
    text += `\u{1F4E6} \u6210\u529F\u66F4\u65B0\u7684\u5305:
`;
    successResults.forEach((result2) => {
      text += `  \u2022 ${result2.module_name}@${result2.version} \u2192 ${result2.appName}
`;
    });
  }
  if (failedResults.length > 0) {
    text += `
\u274C \u5931\u8D25\u7684\u66F4\u65B0:
`;
    failedResults.forEach((result2) => {
      text += `  \u2022 ${result2.module_name}@${result2.version} \u2192 ${result2.appName}
`;
      text += `    \u9519\u8BEF: ${result2.error}
`;
    });
  }
  return text;
}

// src/packages/npm-publish/main-custom.ts
async function mainWithCustomApps(mcpOptions, targetApps) {
  const publishConfig = {
    branch_name: mcpOptions.branch,
    modules: [
      {
        module_name: mcpOptions.npmName,
        version: mcpOptions.npmVersion
      }
    ]
  };
  const customAppMaps = {
    [mcpOptions.npmName]: targetApps
  };
  try {
    Logger.log("Received npm publish data:", publishConfig);
    Logger.log("Target apps:", targetApps);
    const result = await taskWithCustomApps(publishConfig, customAppMaps, mcpOptions.clientDir || "");
    Logger.log("NPM upgrade result:", result);
    const notificationText = generateNotificationText(result, publishConfig);
    notify({
      msg_type: "text",
      content: {
        text: notificationText
      }
    });
    return notificationText;
  } catch (error) {
    console.error("Error processing npm publish:", error);
    const errorText = `\u274C NPM\u5305\u66F4\u65B0\u5904\u7406\u5931\u8D25
\u9519\u8BEF: ${error.message}
\u6570\u636E: ${JSON.stringify(publishConfig)}
\u76EE\u6807\u5E94\u7528: ${targetApps.join(", ")}`;
    notify({
      msg_type: "text",
      content: {
        text: errorText
      }
    });
    return errorText;
  }
}
var main_custom_default = mainWithCustomApps;

// src/mcp-tools/npm-publish.ts
function registerNpmpublishTool(server) {
  server.tool(
    "npm_publish_upgrade_app",
    "\u5F53npm\u5305\u53D1\u5E03\u540E\uFF0C\u63D0\u4F9B\u53D1\u5E03\u5206\u652F\u3001\u5305\u540D\u548C\u7248\u672C\u4EE5\u53CA\u53D1\u5E03\u5E94\u7528\uFF0C\u5373\u53EF\u81EA\u52A8\u5C06\u5BF9\u5E94\u7684\u5E94\u7528\u4E2D\u7684npm\u5305\u5B8C\u6210\u5347\u7EA7\u3002\u652F\u6301\u4E24\u79CD\u4F7F\u7528\u65B9\u5F0F\uFF1A1) \u63D0\u4F9B'apps'\u53C2\u6570\u4E00\u6B65\u5B8C\u6210\uFF0C2) \u8DF3\u8FC7'apps'\u53C2\u6570\u8FDB\u884C\u4E24\u6B65\u6D41\u7A0B\u3002",
    {
      npmName: z.string().describe(
        "\u53D1\u5E03\u5305\u7684npm\u5305\u540D\uFF0C\u901A\u5E38\u5728\u63D0\u4F9B\u7684\u5B57\u7B26\u4E32\u4E2D\u627E\u5230\uFF0C\u683C\u5F0F\u5982\uFF1A/\u5305\u540D\u548C\u7248\u672C\uFF1A<npmName>@<npmVersion>/"
      ),
      npmVersion: z.string().describe(
        "\u53D1\u5E03\u5305\u7684npm\u7248\u672C\u53F7\uFF0C\u901A\u5E38\u5728\u63D0\u4F9B\u7684\u5B57\u7B26\u4E32\u4E2D\u627E\u5230\uFF0C\u683C\u5F0F\u5982\uFF1A/\u5305\u540D\u548C\u7248\u672C\uFF1A<npmName>@<npmVersion>/"
      ),
      branch: z.string().describe(
        "\u5347\u7EA7\u4F7F\u7528\u7684\u5206\u652F\u540D\u79F0\uFF0C\u901A\u5E38\u5728\u63D0\u4F9B\u7684\u5B57\u7B26\u4E32\u4E2D\u627E\u5230\uFF0C\u683C\u5F0F\u5982\uFF1A/\u53D1\u5E03\u5206\u652F\uFF1A<branch>/"
      ),
      clientDir: z.string().optional().default("").describe(
        "\u5BA2\u6237\u7AEF\u76EE\u5F55\u8DEF\u5F84\uFF0C\u53EF\u9009\u53C2\u6570\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\u5B57\u7B26\u4E32\u3002\u4F8B\u5982\uFF1A'client' \u6216 'src/client'"
      ),
      apps: z.string().optional().describe(
        "\u8981\u66F4\u65B0\u7684\u5E94\u7528\u5217\u8868\uFF0C\u591A\u4E2A\u5E94\u7528\u4EE5\u9017\u53F7\u5206\u9694\uFF0C\u4F8B\u5982\uFF1A'app1,app2,app3'\u3002\u5982\u679C\u4E0D\u63D0\u4F9B\uFF0C\u5C06\u5728\u9A8C\u8BC1\u540E\u63D0\u793A\u7528\u6237\u8F93\u5165"
      )
    },
    async ({ npmName, npmVersion, branch, clientDir, apps }) => {
      Logger.log(
        `Starting npm upgrade process: ${npmName}@${npmVersion} on ${branch}, clientDir: ${clientDir || "(default)"}, apps: ${apps || "(not provided)"}`
      );
      try {
        if (!npmName || !npmVersion || !branch) {
          return {
            content: [
              {
                type: "text",
                text: "\u274C \u53C2\u6570\u9A8C\u8BC1\u5931\u8D25\uFF1AnpmName\u3001npmVersion \u548C branch \u90FD\u662F\u5FC5\u9700\u7684\u53C2\u6570"
              }
            ]
          };
        }
        if (branch === "master") {
          return {
            content: [
              {
                type: "text",
                text: "\u274C \u53C2\u6570\u9A8C\u8BC1\u5931\u8D25\uFF1A\u4E0D\u5141\u8BB8\u5728 master \u5206\u652F\u4E0A\u8FDB\u884C\u66F4\u65B0\u64CD\u4F5C"
              }
            ]
          };
        }
        Logger.log(`\u53C2\u6570\u9A8C\u8BC1\u901A\u8FC7: ${npmName}@${npmVersion} on ${branch}`);
        if (apps && apps.trim()) {
          Logger.log(`\u7528\u6237\u5DF2\u63D0\u4F9B\u5E94\u7528\u5217\u8868\uFF0C\u76F4\u63A5\u6267\u884C\u5347\u7EA7: ${apps}`);
          const targetApps = apps.split(",").map((app) => app.trim()).filter((app) => app);
          if (targetApps.length === 0) {
            return {
              content: [
                {
                  type: "text",
                  text: "\u274C \u63D0\u4F9B\u7684\u5E94\u7528\u5217\u8868\u65E0\u6548\uFF0C\u8BF7\u786E\u4FDD\u5E94\u7528\u540D\u79F0\u4EE5\u82F1\u6587\u9017\u53F7\u5206\u9694\u4E14\u4E0D\u4E3A\u7A7A"
                }
              ]
            };
          }
          Logger.log(`\u89E3\u6790\u5230\u7684\u76EE\u6807\u5E94\u7528: ${targetApps.join(", ")}`);
          try {
            const result = await main_custom_default({
              npmName,
              npmVersion,
              branch,
              clientDir
            }, targetApps);
            Logger.log(`\u4E00\u6B65\u5B8C\u6210npm\u5305\u5347\u7EA7`);
            return {
              content: [
                {
                  type: "text",
                  text: `\u2705 npm\u5305\u5347\u7EA7\u4EFB\u52A1\u6267\u884C\u5B8C\u6210

\u76EE\u6807\u5E94\u7528: ${targetApps.join(", ")}
\u5347\u7EA7\u5305: ${npmName}@${npmVersion}
\u5206\u652F: ${branch}
\u5BA2\u6237\u7AEF\u76EE\u5F55: ${clientDir || "(\u9ED8\u8BA4)"}

\u8BE6\u7EC6\u7ED3\u679C:
${result}`
                }
              ]
            };
          } catch (error) {
            Logger.error("\u4E00\u6B65\u5B8C\u6210npm\u5305\u5347\u7EA7\u5931\u8D25:", error);
            return {
              content: [
                {
                  type: "text",
                  text: `\u274C npm\u5305\u5347\u7EA7\u6267\u884C\u5931\u8D25: ${error.message}

\u5347\u7EA7\u5305: ${npmName}@${npmVersion}
\u76EE\u6807\u5E94\u7528: ${targetApps.join(", ")}
\u5206\u652F: ${branch}`
                }
              ]
            };
          }
        } else {
          Logger.log(`\u7528\u6237\u672A\u63D0\u4F9B\u5E94\u7528\u5217\u8868\uFF0C\u4FDD\u5B58\u6570\u636E\u5230\u5185\u5B58\u7B49\u5F85\u540E\u7EED\u6307\u5B9A`);
          const storeKey = `npm_upgrade_${Date.now()}`;
          const upgradeData = {
            npmName,
            npmVersion,
            branch,
            clientDir,
            matchedApps: [],
            // 不再自动匹配，设置为空数组
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          };
          memory_store_default.set(storeKey, upgradeData);
          memory_store_default.set("latest_npm_upgrade", storeKey);
          Logger.log(`\u6570\u636E\u5DF2\u4FDD\u5B58\u5230\u5185\u5B58\uFF0Ckey: ${storeKey}`);
          return {
            content: [
              {
                type: "text",
                text: `\u2705 \u53C2\u6570\u9A8C\u8BC1\u901A\u8FC7\uFF0Cnpm\u5305\u5347\u7EA7\u4EFB\u52A1\u5DF2\u51C6\u5907\u5C31\u7EEA

\u5347\u7EA7\u5305: ${npmName}@${npmVersion}
\u5206\u652F: ${branch}

\u8BF7\u544A\u8BC9\u6211\u4F60\u60F3\u8981\u66F4\u65B0\u54EA\u4E9B\u5E94\u7528\u4EE5\u53CApackage.json\u6240\u5728\u76EE\u5F55\uFF08\u4E0D\u6307\u5B9A\u9ED8\u8BA4\u6839\u76EE\u5F55\uFF09\uFF0C\u591A\u4E2A\u5E94\u7528\u4EE5\u9017\u53F7\u5206\u9694\u3002\u4F8B\u5982\uFF1A\u9700\u8981\u66F4\u65B0\u7684\u5E94\u7528\u5217\u8868\uFF1Aapp1,app2,app3\uFF1B\u76EE\u5F55\uFF1Aclient`
              }
            ]
          };
        }
      } catch (error) {
        Logger.error("npm_publish_upgrade_app \u6267\u884C\u5931\u8D25:", error);
        return {
          content: [
            {
              type: "text",
              text: `\u274C \u5904\u7406\u5931\u8D25: ${error.message}`
            }
          ]
        };
      }
    }
  );
  server.tool(
    "npm_publish_upgrade_app_running",
    "\u624B\u52A8\u6267\u884C\u6307\u5B9A\u5E94\u7528\u7684npm\u5305\u5347\u7EA7\u3002\u5728\u7B2C\u4E00\u4E2A\u5DE5\u5177\u4E4B\u540E\u4F7F\u7528\uFF0C\u7528\u4E8E\u6307\u5B9A\u8981\u5347\u7EA7\u7684\u5E94\u7528\u3002",
    {
      apps: z.string().describe(
        "\u8981\u5347\u7EA7\u7684\u5E94\u7528\u5217\u8868\uFF0C\u7528\u9017\u53F7\u5206\u9694\u7684\u5E94\u7528\u540D\u79F0\uFF0C\u5982\uFF1Aapp1,app2,app3"
      ),
      clientDir: z.string().optional().default("").describe(
        "\u5BA2\u6237\u7AEF\u76EE\u5F55\u8DEF\u5F84\uFF0C\u53EF\u9009\u53C2\u6570\uFF0C\u9ED8\u8BA4\u4E3A\u7A7A\u5B57\u7B26\u4E32\u3002\u5982\u679C\u4E0D\u63D0\u4F9B\uFF0C\u5C06\u4F7F\u7528\u7B2C\u4E00\u4E2A\u5DE5\u5177\u4E2D\u4FDD\u5B58\u7684\u503C"
      )
    },
    async ({ apps, clientDir: overrideClientDir }) => {
      Logger.log(`\u5F00\u59CB\u6267\u884Cnpm\u5305\u5347\u7EA7\u4EFB\u52A1\uFF0C\u76EE\u6807\u5E94\u7528: ${apps}, clientDir: ${overrideClientDir || "(use saved value)"}`);
      try {
        const latestUpgradeKey = memory_store_default.get("latest_npm_upgrade");
        if (!latestUpgradeKey) {
          return {
            content: [
              {
                type: "text",
                text: "\u274C \u672A\u627E\u5230\u5F85\u5904\u7406\u7684npm\u5347\u7EA7\u4EFB\u52A1\uFF0C\u8BF7\u5148\u6267\u884C npm_publish_upgrade_app \u5DE5\u5177\u8FDB\u884C\u53C2\u6570\u9A8C\u8BC1\u548C\u5E94\u7528\u5339\u914D"
              }
            ]
          };
        }
        const upgradeData = memory_store_default.get(latestUpgradeKey);
        if (!upgradeData) {
          return {
            content: [
              {
                type: "text",
                text: "\u274C \u5347\u7EA7\u4EFB\u52A1\u6570\u636E\u5DF2\u8FC7\u671F\u6216\u4E0D\u5B58\u5728\uFF0C\u8BF7\u91CD\u65B0\u6267\u884C npm_publish_upgrade_app \u5DE5\u5177"
              }
            ]
          };
        }
        const { npmName, npmVersion, branch, clientDir: savedClientDir } = upgradeData;
        Logger.log(`\u83B7\u53D6\u5230\u5347\u7EA7\u6570\u636E: ${npmName}@${npmVersion} on ${branch}`);
        const finalClientDir = overrideClientDir !== void 0 ? overrideClientDir : savedClientDir || "";
        Logger.log(`\u4F7F\u7528\u7684 clientDir: ${finalClientDir || "(default)"}`);
        const targetApps = apps.split(",").map((app) => app.trim()).filter((app) => app);
        if (targetApps.length === 0) {
          return {
            content: [
              {
                type: "text",
                text: "\u274C \u672A\u6307\u5B9A\u6709\u6548\u7684\u5E94\u7528\u540D\u79F0\uFF0C\u8BF7\u63D0\u4F9B\u9017\u53F7\u5206\u9694\u7684\u5E94\u7528\u5217\u8868\uFF0C\u4F8B\u5982\uFF1Aapp1,app2,app3"
              }
            ]
          };
        }
        Logger.log(`\u7528\u6237\u6307\u5B9A\u5E94\u7528: ${targetApps.join(", ")}`);
        Logger.log(`\u5F00\u59CB\u6267\u884Cnpm\u5305\u5347\u7EA7...`);
        const result = await main_custom_default({
          npmName,
          npmVersion,
          branch,
          clientDir: finalClientDir
        }, targetApps);
        memory_store_default.delete(latestUpgradeKey);
        memory_store_default.delete("latest_npm_upgrade");
        Logger.log(`npm\u5305\u5347\u7EA7\u5B8C\u6210`);
        return {
          content: [
            {
              type: "text",
              text: `\u2705 npm\u5305\u5347\u7EA7\u4EFB\u52A1\u6267\u884C\u5B8C\u6210

\u76EE\u6807\u5E94\u7528: ${targetApps.join(", ")}
\u5347\u7EA7\u5305: ${npmName}@${npmVersion}
\u5206\u652F: ${branch}

\u8BE6\u7EC6\u7ED3\u679C:
${result}`
            }
          ]
        };
      } catch (error) {
        Logger.error("npm_publish_upgrade_app_running \u6267\u884C\u5931\u8D25:", error);
        return {
          content: [
            {
              type: "text",
              text: `\u274C npm\u5305\u5347\u7EA7\u6267\u884C\u5931\u8D25: ${error.message}

\u4F60\u53EF\u4EE5\u91CD\u65B0\u5C1D\u8BD5\u6267\u884C\u6B64\u5DE5\u5177\uFF0C\u6216\u8005\u91CD\u65B0\u5F00\u59CB\u6574\u4E2A\u6D41\u7A0B`
            }
          ]
        };
      }
    }
  );
}

// src/mcp-tools/create-branch.ts
import { z as z2 } from "zod";

// src/packages/create-branch/constants.ts
var PARENT_APPS = ["wsc-tee-h5", "wsc"];

// src/packages/create-branch/notification.ts
import { execSync as execSync3 } from "child_process";
function getGitUserName2() {
  try {
    return execSync3("git config user.name", { encoding: "utf8" }).trim();
  } catch (error) {
    console.warn("Failed to get git user name:", error.message);
    return "Unknown User";
  }
}
function generateNotificationText2(result, originalData) {
  const { branch_name } = originalData;
  const user_name = getGitUserName2();
  if (!result.success && (!result.results || result.results.length === 0)) {
    return `\u274C \u66F4\u65B0\u5931\u8D25
\u7528\u6237: ${user_name}
\u5206\u652F: ${branch_name}
\u9519\u8BEF: ${result.error || "\u672A\u77E5\u9519\u8BEF"}`;
  }
  const successResults = result.results?.filter(
    /** @param {NotificationOperationResult} r */
    (r) => r.success
  ) || [];
  const failedResults = result.results?.filter(
    /** @param {NotificationOperationResult} r */
    (r) => !r.success
  ) || [];
  const successfulOperations = successResults.length;
  const failedOperations = failedResults.length;
  const title = result.success ? "\u2705 \u5E94\u7528\u5206\u652F\u66F4\u65B0\u5B8C\u6210" : "\u26A0\uFE0F \u5E94\u7528\u5206\u652F\u66F4\u65B0\u90E8\u5206\u5B8C\u6210";
  let text = `${title}
`;
  text += `\u7528\u6237: ${user_name}
`;
  text += `\u5206\u652F: ${branch_name}
`;
  text += `\u603B\u8BA1: ${successfulOperations}\u6210\u529F / ${failedOperations}\u5931\u8D25

`;
  if (successResults.length > 0) {
    text += `\u{1F4E6} \u6210\u529F\u66F4\u65B0\u7684\u5E94\u7528:
`;
    successResults.forEach(
      /** @param {NotificationOperationResult} result */
      (result2) => {
        text += `  \u2022 ${result2.appName}
`;
      }
    );
  }
  if (failedResults.length > 0) {
    text += `
\u274C \u5931\u8D25\u7684\u66F4\u65B0:
`;
    failedResults.forEach(
      /** @param {NotificationOperationResult} result */
      (result2) => {
        text += `  \u2022 ${result2.appName}
`;
        text += `    \u9519\u8BEF: ${result2.error}
`;
      }
    );
  }
  return text;
}

// src/packages/create-branch/main.ts
async function _createBranch(options) {
  const { appNames, branchName, wscTargetBranchName = "master" } = options;
  Logger.log(`\u5F00\u59CB\u521B\u5EFA\u5206\u652F ${branchName}\uFF0C\u57FA\u4E8E\u5206\u652F ${wscTargetBranchName}`);
  Logger.log(`\u6D89\u53CA\u5E94\u7528: ${appNames.join(", ")}`);
  if (!Array.isArray(appNames) || appNames.length === 0) {
    console.error("appNames\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u6570\u7EC4");
    throw new Error("appNames\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u6570\u7EC4");
  }
  if (typeof branchName !== "string" || !branchName.trim()) {
    throw new Error("branchName\u5FC5\u987B\u662F\u4E00\u4E2A\u975E\u7A7A\u5B57\u7B26\u4E32");
  }
  const results = [];
  const parentUpdates = {};
  for (const appName of appNames) {
    try {
      if (!getProjectId(appName)) {
        throw new Error(`\u5E94\u7528${appName}\u4E0D\u5B58\u5728\u4E8E\u914D\u7F6E\u4E2D`);
      }
      Logger.log(`\u5F00\u59CB\u4E3A\u5E94\u7528${appName}\u521B\u5EFA\u5206\u652F${branchName}...`);
      const response = await createBranch({
        appName,
        branchName,
        targetBranchName: appName === "wsc" ? wscTargetBranchName : "master"
      });
      if (response.message) {
        throw new Error(`\u521B\u5EFA\u5931\u8D25: ${response.message}`);
      }
      results.push({
        appName,
        success: true,
        branch: response,
        message: `\u5206\u652F${branchName}\u521B\u5EFA\u6210\u529F`
      });
      Logger.log(`\u5E94\u7528${appName}\u7684\u5206\u652F${branchName}\u521B\u5EFA\u6210\u529F`);
      for (const parentApp of PARENT_APPS) {
        if (!parentUpdates[parentApp]) {
          parentUpdates[parentApp] = [];
        }
        parentUpdates[parentApp].push(appName);
        Logger.log(`\u7F13\u5B58\u7236\u5E94\u7528${parentApp}\u5BF9\u5B50\u5E94\u7528${appName}\u7684\u66F4\u65B0\u9700\u6C42`);
      }
    } catch (error) {
      const err = error;
      results.push({
        appName,
        success: false,
        error: err.message,
        message: `\u5206\u652F${branchName}\u521B\u5EFA\u5931\u8D25: ${error.message}`
      });
      console.error(`\u5E94\u7528${appName}\u7684\u5206\u652F${branchName}\u521B\u5EFA\u5931\u8D25:`, err.message);
    }
  }
  for (const [parentApp, subApps] of Object.entries(parentUpdates)) {
    try {
      await updateAppKokoRepo({
        appName: parentApp,
        branchName,
        subApps
      });
    } catch (error) {
      console.error(`\u66F4\u65B0\u7236\u5E94\u7528${parentApp}\u5931\u8D25:`, error.message);
    }
  }
  Logger.log(`\u5206\u652F ${branchName} \u521B\u5EFA\u5B8C\u6210\uFF0C\u5171\u5904\u7406 ${appNames.length} \u4E2A\u5E94\u7528`);
  return results;
}
async function main(options) {
  try {
    const { biz, branchName } = options;
    const appNames = biz;
    const allAppNames = [.../* @__PURE__ */ new Set([...appNames, ...PARENT_APPS])];
    Logger.log(`\u89E3\u6790\u5F97\u5230\u7684appNames: ${allAppNames.join(", ")}`);
    const branch = await getWscLastHotfixBranch();
    Logger.log(`\u83B7\u53D6\u5230\u7684\u6700\u65B0hotfix\u5206\u652F\u662F: ${branch}`);
    const results = await _createBranch({
      appNames: allAppNames,
      branchName,
      wscTargetBranchName: branch
    });
    const notificationText = generateNotificationText2(
      {
        success: results.every((r) => r.success),
        results,
        error: results.find((r) => !r.success)?.error
      },
      {
        branch_name: branchName
      }
    );
    await notify({
      msg_type: "text",
      content: {
        text: notificationText
      }
    });
    return results;
  } catch (error) {
    await notify({
      msg_type: "text",
      content: {
        text: `\u274C \u5206\u652F\u521B\u5EFA\u5931\u8D25
\u5206\u652F: ${options.branchName}
\u9519\u8BEF: ${error.message}`
      }
    });
    throw error;
  }
}

// library/branch.json
var branch_default = {
  subBranchs: [
    "ext-tee-wsc-decorate",
    "ext-tee-wsc-decorate-h5",
    "ext-tee-wsc-statcenter",
    "ext-tee-assets",
    "ext-tee-passport",
    "ext-tee-shop",
    "ext-tee-wsc-goods",
    "ext-tee-wsc-ump",
    "ext-tee-salesman",
    "ext-tee-logger",
    "ext-tee-wsc-im",
    "ext-tee-wsc-trade",
    "ext-tee-cps",
    "ext-tee-retail-prepaid",
    "ext-tee-guide",
    "ext-tee-navigate",
    "ext-tee-edu-goods",
    "ext-tee-retail-groupbuy",
    "ext-tee-retail-solitaire",
    "ext-tee-wholesale",
    "ext-tee-common",
    "ext-tee-user",
    "ext-tee-retail-shelf",
    "ext-tee-live"
  ]
};

// src/mcp-tools/create-branch.ts
function registerCreateBranchTool(server) {
  server.tool(
    "create_ranta_h5_branch",
    "\u5F53\u9700\u8981\u4E3A\u5E94\u7528\u521B\u5EFAH5\u5206\u652F\u5E76\u4E14\u6709\u7236\u5206\u652F\u65F6\u4F7F\u7528\u6B64\u5DE5\u5177",
    {
      branchName: z2.string().describe(
        "\u8981\u521B\u5EFA\u7684\u5206\u652F\u540D\u79F0\uFF0C\u4F8B\u5982\uFF1A\u521B\u5EFA\u5206\u652F: <branchName>\uFF0C\u5305\u542Bxx\u4E1A\u52A1"
      ),
      biz: z2.array(
        z2.string().describe(
          `\u8981\u5305\u542B\u7684\u4E1A\u52A1\u6A21\u5757\uFF0C\u4F8B\u5982\uFF1A\u521B\u5EFA\u5206\u652F: hotfix/xxx\uFF0C\u5305\u542B<biz>\u4E1A\u52A1\uFF0C\u4E1A\u52A1\u6A21\u5757\u6765\u81EA: ${branch_default.subBranchs.join(", ")}`
        )
      )
    },
    async ({ branchName, biz }) => {
      Logger.log(`Creating branch ${branchName} on ${biz}`);
      await main({ branchName, biz });
      return {
        content: [
          {
            type: "text",
            text: "Successfully created branch."
          }
        ]
      };
    }
  );
}

// src/mcp-tools/index.ts
function registerTools(server) {
  registerNpmpublishTool(server);
  registerCreateBranchTool(server);
}

// src/cli.ts
config({ path: resolve2(process.cwd(), ".env") });
function createServer(isHTTP = false) {
  const server = new McpServer2({
    name: "front-development-tools-mcp Server",
    version: "2.0.1"
  });
  registerTools(server);
  Logger.isHTTP = isHTTP;
  return server;
}
async function startServer() {
  const isStdioMode = process.env.NODE_ENV === "cli" || process.argv.includes("--stdio");
  const config2 = getServerConfig(isStdioMode);
  const server = createServer(!isStdioMode);
  if (isStdioMode) {
    const transport = new StdioServerTransport();
    await server.connect(transport);
  } else {
    Logger.log(
      `Initializing front-development-tools-mcp MCP Server in HTTP mode on port ${config2.port}...`
    );
    await startHttpServer(config2.port, server);
  }
}
if (process.argv[1]) {
  startServer().catch((error) => {
    Logger.error("Failed to start server:", error);
    process.exit(1);
  });
}

export {
  getServerConfig,
  createServer,
  startServer
};
