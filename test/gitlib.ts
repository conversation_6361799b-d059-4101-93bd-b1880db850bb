import { getWscLastHotfixBranch, getProjectRepo, getFileContent, updateAppKokoRepo, updatePackageJson, parseGitLabMrUrl, analyzeGitLabMrComments } from "../src/gitlib/tools.js";
import { getProjectInfo, createBranch } from "../src/gitlib/api.js";

export const getWscLastHotfixBranchTest = () => {
  return getWscLastHotfixBranch();
};

export const getProjectRepoTest = () => {
  return getProjectRepo("wsc-pc-trade");
};

export const getProjectInfoTest = () => {
  return getProjectInfo({ appName: "wsc-tee-h5" });
};

export const getRepositoryFileContentTest = () => {
  return getFileContent({
    appName: "wsc-tee-h5",
    filePath: "koko.repo.json",
    branch: "master",
  });
};


/**
 * 生成测试分支并更新koko.repo.json文件内容
 */
export const updateAppKokoRepoTest = () => {
  const branchName = "hotfix/test-" + Date.now();
  return createBranch({
    appName: "wsc-tee-h5",
    branchName,
    targetBranchName: "master",
  }).then(() => {
    return updateAppKokoRepo({
      appName: "wsc-tee-h5",
      branchName,
      subApps: ["ext-tee-wsc-ump", "ext-tee-wsc-trade"],
    });
  });
};

/**
 * 生成测试分支并更新package.json文件内容
 */
export const updatePackageJsonTest = () => {
  const branchName = "hotfix/test-" + Date.now();
  return createBranch({
    appName: "wsc-pc-trade",
    branchName,
  }).then(() => {
    return updatePackageJson({
      appName: "wsc-pc-trade",
      branchName,
      path: 'client',
      packageInfo: {
        name: "@youzan/order-domain-pc-components",
        version: "1.1.14-beta.20250911141634.0",
      },
    });
  });
};

/**
 * 测试GitLab MR URL解析功能
 */
export const parseGitLabMrUrlTest = () => {
  const testUrl = "https://gitlab.qima-inc.com/weapp/wsc/-/merge_requests/123";
  try {
    const result = parseGitLabMrUrl(testUrl);
    console.log("URL解析结果:", result);
    return result;
  } catch (error: any) {
    console.error("URL解析失败:", error.message);
    throw error;
  }
};

/**
 * 测试GitLab MR评论分析功能
 * 注意：这需要一个真实的MR URL才能测试
 */
export const analyzeGitLabMrCommentsTest = () => {
  // 这里需要替换为一个真实的MR URL进行测试
  const testUrl = "https://gitlab.qima-inc.com/weapp/ext-tee-wsc-trade/-/merge_requests/9638";

  console.log("开始测试MR评论分析...");
  console.log("测试URL:", testUrl);
  console.log("注意：请确保该MR存在且有评论数据");

  return analyzeGitLabMrComments(testUrl)
    .then((result) => {
      console.log("MR评论分析结果:");
      console.log(result);
      return result;
    })
    .catch((error: any) => {
      console.error("MR评论分析失败:", error.message);
      throw error;
    });
};
