/**
 * 创建H5中台化分支
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";
import { __dirname } from "~/utils/patch.js";

import createBranch from "../packages/create-branch/main.js";
import branchData from '../../library/branch.json';

export default function registerCreateBranchTool(server: any) {
  server.tool(
    "create_ranta_h5_branch",
    "当需要为应用创建H5分支并且有父分支时使用此工具",
    {
      branchName: z
        .string()
        .describe(
          "要创建的分支名称，例如：创建分支: <branchName>，包含xx业务"
        ),
      biz: z.array(
        z
          .string()
          .describe(
            `要包含的业务模块，例如：创建分支: hotfix/xxx，包含<biz>业务，业务模块来自: ${branchData.subBranchs.join(', ')}`
          )
      ),
    },
    async ({ branchName, biz }: any) => {
      Logger.log(`Creating branch ${branchName} on ${biz}`);
      await createBranch({ branchName, biz });
      return {
        content: [
          {
            type: "text",
            text: "Successfully created branch.",
          },
        ],
      };
    }
  );
}
