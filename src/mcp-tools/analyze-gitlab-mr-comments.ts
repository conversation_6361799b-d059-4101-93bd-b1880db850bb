/**
 * 分析GitLab Merge Request的评论并生成代码修改指导
 */
import { z } from "zod";
import { Logger } from "~/utils/logger.js";
import { analyzeGitLabMrComments } from "../gitlib/tools.js";

export default function registerAnalyzeGitLabMrCommentsTool(server: any) {
  server.tool(
    "analyze_gitlab_mr_comments",
    "当用户提供GitLab Merge Request URL并说明'根据评论修改'时，分析MR中的评论并生成代码修改指导",
    {
      mrUrl: z
        .string()
        .describe(
          "GitLab Merge Request的完整URL，例如：https://gitlab.qima-inc.com/group/project/-/merge_requests/123"
        ),
      action: z
        .string()
        .describe(
          "用户的操作意图，应该是'根据评论修改'来确认用户想要分析评论"
        ),
    },
    async ({ mrUrl, action }: { mrUrl: string; action: string }) => {
      try {
        Logger.log(`Analyzing GitLab MR comments for: ${mrUrl}`);
        Logger.log(`User action: ${action}`);

        // 验证action参数
        if (action !== "根据评论修改") {
          return {
            content: [
              {
                type: "text",
                text: "错误：此工具仅在用户明确表示'根据评论修改'时使用。",
              },
            ],
          };
        }

        // 验证URL格式
        if (!mrUrl.includes("gitlab.qima-inc.com") || !mrUrl.includes("merge_requests")) {
          return {
            content: [
              {
                type: "text",
                text: "错误：请提供有效的GitLab Merge Request URL。",
              },
            ],
          };
        }

        // 分析评论
        const analysisResult = await analyzeGitLabMrComments(mrUrl);

        Logger.log("Successfully analyzed MR comments");

        return {
          content: [
            {
              type: "text",
              text: `GitLab MR评论分析结果：\n\n${analysisResult}`,
            },
          ],
        };
      } catch (error: any) {
        Logger.error(`Failed to analyze GitLab MR comments: ${error.message}`);
        
        return {
          content: [
            {
              type: "text",
              text: `分析GitLab MR评论失败：${error.message}`,
            },
          ],
        };
      }
    }
  );
}
