import fetch from "node-fetch";
import { getProjectId } from "./tools.js";

/**
 * 发送HTTP请求到指定的URL，带有重试机制
 *
 * @param path 要请求的path。
 * @param options 请求的配置选项，可选参数。
 * @returns 返回Promise对象，解析为响应数据的JSON对象。
 */
function request(path: string, options: any = {}): Promise<any> {
  const rootUrl = "https://gitlab.qima-inc.com/api/v4";
  const url = `${rootUrl}${path}`;
  const maxRetries = 3;
  
  const makeRequest = async (attempt: number = 1): Promise<any> => {
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "PRIVATE-TOKEN": process.env.private_token,
        },
        ...options,
      });

      // Check if response is ok (status 200-299)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error: any) {
      console.error(`[GitLab API] Attempt ${attempt} failed for ${path}:`, error.message);
      
      if (attempt >= maxRetries) {
        console.error(`[GitLab API] All ${maxRetries} attempts failed for ${path}`);
        throw error;
      }
      
      const delay = attempt * 300;
      console.log(`[GitLab API] Retrying in ${delay}ms... (attempt ${attempt + 1}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      return makeRequest(attempt + 1);
    }
  };

  return makeRequest();
}

/**
 * 创建一个新的分支
 *
 * @param {Object} params 参数对象
 * @param {string} params.appName 应用名称
 * @param {string} params.branchName 新分支名称
 * @param {string} params.targetBranchName 目标分支名称（新分支将基于这个分支创建）
 * @returns {Promise<Object>} 返回包含创建分支结果的 Promise 对象
 */
export const createBranch = ({
  appName,
  branchName,
  targetBranchName = 'master',
}: any): Promise<any> => {
  const projectId = getProjectId(appName);

  return request(
    `/projects/${projectId}/repository/branches?branch=${branchName}&ref=${targetBranchName}`
  );
};

/**
 * 获取分支信息
 *
 * @param appName 应用名称
 * @param branchName 分支名称
 * @returns 分支信息
 */
export const getBranch = ({ appName, branchName }: any): Promise<any> => {
  const encodeBranchName = encodeURIComponent(branchName);
  const projectId = getProjectId(appName);
  return request(`/projects/${projectId}/repository/branches/${encodeBranchName}`, {
    method: "GET",
  });
}

/**
 * 获取分支信息
 *
 * @param options 参数对象
 * @param options.appName 应用名称
 * @param options.search 搜索关键词
 * @returns 分支信息
 */
export const getBranchs = ({ appName, search }: any): Promise<any> => {
  const projectId = getProjectId(appName || "wsc");
  return request(
    `/projects/${projectId}/repository/branches?search=` +
      encodeURIComponent(search),
    {
      method: "GET",
    }
  );
};

/**
 * 获取应用详情
 *
 * @param appName 应用名称
 * @returns 返回信息
 */
export const getProjectInfo = async ({ appName }: any): Promise<any> => {
  const projectId = getProjectId(appName);
  return request(`/projects/${projectId}`, {
    method: "GET",
  });
};

/**
 * 从指定应用的代码仓库中获取文件内容
 *
 * @param appName 应用名称
 * @param filePath 文件路径
 * @param branch 分支名称
 * @returns 返回获取到的文件内容
 */
export const getRepositoryFileContent = async ({
  appName,
  filePath,
  branch,
}: any): Promise<any> => {
  const projectId = getProjectId(appName);
  return request(
    `/projects/${projectId}/repository/files/${encodeURIComponent(
      filePath
    )}?ref=${branch}`,
    {
      method: "GET",
    }
  );
};


/**
 * 更新仓库中指定文件的内容
 *
 * @param appName 应用名称
 * @param filePath 文件路径
 * @param branch 分支名称
 * @param content 文件内容
 * @param commit_message 提交信息
 * @returns 返回一个 Promise 对象，解析为更新后的文件内容
 */
export const updateRepositoryFileContent = async ({
  appName,
  filePath,
  branch,
  content,
  commit_message,
}: any): Promise<any> => {
  const projectId = getProjectId(appName);
  return request(
    `/projects/${projectId}/repository/files/${encodeURIComponent(filePath)}`,
    {
      method: "PUT",
      body: JSON.stringify({
        branch,
        content,
        commit_message,
      }),
    }
  );
};

/**
 * 创建新的GitLab Pipeline
 *
 * @param {Object} options 参数对象
 * @param {string} options.appName 应用名称
 * @param {string} options.branch 分支名称
 * @param {Object} [options.variables] Pipeline变量
 * @returns {Promise<Object>} 返回包含新Pipeline信息的Promise对象
 */
export const createPipeline = async ({
  appName,
  branch,
  variables = {}
}: {
  appName: string;
  branch: string;
  variables?: Record<string, string>;
}): Promise<any> => {
  const projectId = getProjectId(appName);

  return request(`/projects/${projectId}/pipeline`, {
    method: "POST",
    body: JSON.stringify({
      ref: branch,
      variables: Object.entries(variables).map(([key, value]) => ({
        key,
        value: String(value)
      }))
    })
  });
};

/**
 * 获取Merge Request信息
 *
 * @param {Object} params 参数对象
 * @param {number} params.projectId 项目ID
 * @param {number} params.mergeRequestIid Merge Request的内部ID
 * @returns {Promise<Object>} 返回包含MR信息的Promise对象
 */
export const getMergeRequest = async ({
  projectId,
  mergeRequestIid,
}: {
  projectId: number;
  mergeRequestIid: number;
}): Promise<any> => {
  return request(`/projects/${projectId}/merge_requests/${mergeRequestIid}`, {
    method: "GET",
  });
};

/**
 * 获取Merge Request的所有discussions
 *
 * @param {Object} params 参数对象
 * @param {number} params.projectId 项目ID
 * @param {number} params.mergeRequestIid Merge Request的内部ID
 * @returns {Promise<Array>} 返回包含所有discussions的Promise对象
 */
export const getMergeRequestDiscussions = async ({
  projectId,
  mergeRequestIid,
}: {
  projectId: number;
  mergeRequestIid: number;
}): Promise<any[]> => {
  return request(`/projects/${projectId}/merge_requests/${mergeRequestIid}/discussions`, {
    method: "GET",
  });
};
