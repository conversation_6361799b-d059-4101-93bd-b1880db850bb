import {
  getBranchs,
  getProjectInfo,
  getRepositoryFileContent,
  updateRepositoryFileContent,
  getMergeRequest,
  getMergeRequestDiscussions,
} from "./api.js";

import gitProjects from "./git-projects.json";

// 获取wsc最近一次hotfix分支
export function getWscLastHotfixBranch(): Promise<any> {
  return getBranchs({ appName: "wsc", search: "^hotfix/v2.2" })
    .then((branches) =>
      branches.sort(
        (a: any, b: any) =>
          +new Date(b.commit.created_at) - +new Date(a.commit.created_at)
      )
    )
    .then((sortedBranches) => sortedBranches[0].name);
}

// 获取项目仓库地址
export function getProjectRepo(appName: any): Promise<any> {
  return getProjectInfo({
    appName,
  }).then((res) => res.ssh_url_to_repo);
}

// 获取项目id
export function getProjectId(appName: any) {
  return gitProjects.find((p: any) => p.name === appName)?.id;
}

// 获取文件内容
export function getFileContent({ appName, filePath, branch }: any) {
  return getRepositoryFileContent({
    appName,
    filePath,
    branch,
  }).then((res) => Buffer.from(res.content, "base64").toString());
}

// 更新gitlab仓库的koko.repo.json文件内容
export async function updateAppKokoRepo(options: any): Promise<any> {
  const { appName, branchName, subApps } = options;

  // 验证参数
  if (!appName || !branchName || !Array.isArray(subApps)) {
    throw new Error("appName, branchName和subApps都是必填参数");
  }

  return getFileContent({
    appName,
    filePath: "koko.repo.json",
    branch: branchName,
  }).then((content) => {
    const kokoRepo = JSON.parse(content);
    kokoRepo.repos.forEach((repo: any) => {
      if (subApps.includes(repo.name)) {
        repo.branch = branchName;
      }
    });
    return updateRepositoryFileContent({
      appName,
      filePath: "koko.repo.json",
      branch: branchName,
      content: JSON.stringify(kokoRepo, null, 2),
      commit_message: `feat: 更新${subApps}的分支为${branchName}`,
    });
  });
}

/**
 * 更新package.json文件内容
 * @param appName 仓库名称
 * @param branchName 分支名称
 * @param packageInfo 依赖信息
 * @returns 
 */
export async function updatePackageJson({
  appName,
  branchName,
  path,
  packageInfo,
}: any) {
  const filePath  = path ? `${path}/package.json` : "package.json";
  return getFileContent({
    appName,
    filePath,
    branch: branchName,
  }).then((content) => {
    const packageJson = JSON.parse(content);
    const { name, version } = packageInfo;

    // 检查是否在任何依赖类型中找到了该包
    let packageFound = false;

    if (packageJson?.dependencies?.[name]) {
      packageJson.dependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.devDependencies?.[name]) {
      packageJson.devDependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.peerDependencies?.[name]) {
      packageJson.peerDependencies[name] = version;
      packageFound = true;
    }
    if (packageJson?.resolutions?.[name]) {
      packageJson.resolutions[name] = version;
      packageFound = true;
    }

    // 如果没有找到该包，抛出错误
    if (!packageFound) {
      throw new Error(`${appName}/${filePath}中没有找到${name}的信息，请检查目录是否正确。`);
    }

    return updateRepositoryFileContent({
      appName,
      filePath,
      branch: branchName,
      content: JSON.stringify(packageJson, null, 2),
      commit_message: `feat: 更新${name}的依赖版本为${version}`,
    });
  });
}

/**
 * 解析GitLab MR URL，提取项目信息和MR ID
 * @param mrUrl GitLab MR的完整URL
 * @returns 包含projectId和mergeRequestIid的对象
 */
export function parseGitLabMrUrl(mrUrl: string): { projectId: number; mergeRequestIid: number } {
  try {
    // 匹配GitLab MR URL格式: https://gitlab.qima-inc.com/group/project/-/merge_requests/123
    const urlPattern = /https:\/\/gitlab\.qima-inc\.com\/(.+)\/-\/merge_requests\/(\d+)/;
    const match = mrUrl.match(urlPattern);

    if (!match) {
      throw new Error("无效的GitLab MR URL格式");
    }

    const projectPath = match[1]; // group/project
    const mergeRequestIid = parseInt(match[2], 10);

    // 根据项目路径查找项目ID
    const project = gitProjects.find((p: any) => p.path_with_namespace === projectPath);

    if (!project) {
      throw new Error(`未找到项目: ${projectPath}`);
    }

    return {
      projectId: project.id,
      mergeRequestIid,
    };
  } catch (error: any) {
    throw new Error(`解析MR URL失败: ${error.message}`);
  }
}

/**
 * 分析GitLab MR的评论，配对问题与回复
 * @param mrUrl GitLab MR的完整URL
 * @returns 格式化的问题-结论对字符串
 */
export async function analyzeGitLabMrComments(mrUrl: string): Promise<string> {
  try {
    // 解析URL获取项目ID和MR ID
    const { projectId, mergeRequestIid } = parseGitLabMrUrl(mrUrl);

    // 获取MR信息，确定真正的代码作者（分支创建者）
    const mrInfo = await getMergeRequest({ projectId, mergeRequestIid });

    // MR的author可能是工具（如let-ai-cr），我们需要找到真正的代码作者
    // 通常是分支的创建者或者第一个提交的作者
    let codeAuthorId = mrInfo.author.id;
    let codeAuthorName = mrInfo.author.name;

    // 如果MR作者是工具账号，尝试从分支名或其他信息推断真正的作者
    if (mrInfo.author.username && (
        mrInfo.author.username.includes('let-ai-cr') ||
        mrInfo.author.username.includes('bot') ||
        mrInfo.author.username.includes('tool')
    )) {
      console.log(`[DEBUG] 检测到工具账号: ${mrInfo.author.name}, 需要查找真正的代码作者`);

      // 从分支名推断作者，或者使用其他逻辑
      // 这里我们先使用一个简化的方法：查看discussions中最活跃的非工具用户
      const discussions = await getMergeRequestDiscussions({ projectId, mergeRequestIid });
      const userActivity = new Map<number, { name: string; count: number }>();

      for (const discussion of discussions) {
        if (discussion.notes) {
          for (const note of discussion.notes) {
            if (!note.system && note.author &&
                !note.author.username?.includes('let-ai-cr') &&
                !note.author.username?.includes('bot') &&
                !note.author.username?.includes('tool')) {
              const userId = note.author.id;
              const current = userActivity.get(userId) || { name: note.author.name, count: 0 };
              userActivity.set(userId, { name: current.name, count: current.count + 1 });
            }
          }
        }
      }

      // 找到最活跃的用户作为代码作者
      let maxActivity = 0;
      for (const [userId, activity] of userActivity.entries()) {
        if (activity.count > maxActivity) {
          maxActivity = activity.count;
          codeAuthorId = userId;
          codeAuthorName = activity.name;
        }
      }

      console.log(`[DEBUG] 推断的代码作者: ${codeAuthorName} (ID: ${codeAuthorId})`);
    } else {
      console.log(`[DEBUG] MR作者即为代码作者: ${codeAuthorName} (ID: ${codeAuthorId})`);
    }

    // 获取所有discussions（如果之前没有获取过）
    let discussions;
    if (mrInfo.author.username && (
        mrInfo.author.username.includes('let-ai-cr') ||
        mrInfo.author.username.includes('bot') ||
        mrInfo.author.username.includes('tool')
    )) {
      // 已经在上面获取过了
      discussions = await getMergeRequestDiscussions({ projectId, mergeRequestIid });
    } else {
      discussions = await getMergeRequestDiscussions({ projectId, mergeRequestIid });
    }

    console.log(`[DEBUG] 找到 ${discussions.length} 个讨论`);

    // 分析并配对评论
    const commentPairs: Array<{
      question: string;
      answer: string;
      questionTime: string;
      answerTime?: string;
      questionAuthor: string;
      answerAuthor?: string;
    }> = [];

    for (const discussion of discussions) {
      if (!discussion.notes || discussion.notes.length === 0) {
        continue;
      }

      // 按时间排序notes
      const sortedNotes = discussion.notes.sort((a: any, b: any) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      console.log(`[DEBUG] 讨论包含 ${sortedNotes.length} 条评论`);

      // 遍历所有评论，寻找问题和回复的配对
      for (let i = 0; i < sortedNotes.length; i++) {
        const note = sortedNotes[i];

        // 跳过系统消息和空内容
        if (note.system || !note.body || note.body.trim() === '') {
          continue;
        }

        // 如果这是非作者的评论，视为问题
        if (note.author.id !== codeAuthorId) {
          console.log(`[DEBUG] 发现问题 - 作者: ${note.author.name}, 内容: ${note.body.trim().substring(0, 50)}...`);

          // 查找作者在此评论之后的回复
          let authorReply = null;
          for (let j = i + 1; j < sortedNotes.length; j++) {
            const laterNote = sortedNotes[j];
            if (laterNote.author.id === codeAuthorId &&
                laterNote.body &&
                laterNote.body.trim() !== '' &&
                !laterNote.system) {
              authorReply = laterNote;
              console.log(`[DEBUG] 找到作者回复: ${laterNote.body.trim().substring(0, 50)}...`);
              break;
            }
          }

          const pair = {
            question: note.body.trim(),
            answer: authorReply ? authorReply.body.trim() : "待回复",
            questionTime: note.created_at,
            answerTime: authorReply ? authorReply.created_at : undefined,
            questionAuthor: note.author.name,
            answerAuthor: authorReply ? authorReply.author.name : undefined,
          };

          commentPairs.push(pair);
        }
      }
    }

    // 按问题时间排序
    commentPairs.sort((a, b) =>
      new Date(a.questionTime).getTime() - new Date(b.questionTime).getTime()
    );

    console.log(`[DEBUG] 总共找到 ${commentPairs.length} 个问题-回复对`);

    // 格式化输出
    if (commentPairs.length === 0) {
      return "未找到需要处理的评论问题。";
    }

    let result = "";
    commentPairs.forEach((pair, index) => {
      result += `问题${index + 1}：${pair.question}\n`;
      result += `修改结论${index + 1}：${pair.answer}\n\n`;
    });

    return result.trim();

  } catch (error: any) {
    throw new Error(`分析MR评论失败: ${error.message}`);
  }
}
